"""
流体路径规划系统 v2.0 - 主入口文件
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """主函数"""
    print("流体路径规划系统 v2.0")
    print("="*50)
    print("可用的运行选项:")
    print("1. python examples/demo.py - 运行基础演示")
    print("2. python examples/run_and_save_results.py - 批量测试并保存结果")
    print("3. 查看 docs/README.md 获取详细使用说明")
    print("4. 查看 result/ 文件夹中的测试结果")
    print("="*50)
    
    # 导入测试
    try:
        from fluid_path_planner import FluidPathPlanner
        print("✓ 模块导入测试成功")
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return
    
    print("\n系统就绪！请选择上述选项之一开始使用。")

if __name__ == "__main__":
    main()
