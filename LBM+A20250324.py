import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import time
import multiprocessing as mp
from functools import partial
import os
import heapq  # 用于实现优先队列
from scipy.ndimage import gaussian_filter  # 用于平滑速度场

# 创建自定义颜色映射，用于更好的可视化效果
def create_custom_colormap():
    # 创建自定义颜色映射，从蓝色到红色，中间经过白色
    colors = [(0, 0, 0.8), (0, 0.5, 1), (0.9, 0.9, 1), (1, 0.5, 0), (0.8, 0, 0)]
    return LinearSegmentedColormap.from_list('custom_cmap', colors, N=256)

# D2Q9模型的速度集
c = np.array([
    [0, 0],    # 静止
    [1, 0],    # 东
    [0, 1],    # 北
    [-1, 0],   # 西
    [0, -1],   # 南
    [1, 1],    # 东北
    [-1, 1],   # 西北
    [-1, -1],  # 西南
    [1, -1]    # 东南
])

# D2Q9权重
w = np.array([4/9, 1/9, 1/9, 1/9, 1/9, 1/36, 1/36, 1/36, 1/36])

# 反向索引，用于反弹边界条件
opposite = np.array([0, 3, 4, 1, 2, 7, 8, 5, 6])

def add_boundary(grid_map):
    """
    在地图四周添加障碍物边界，并在右下角和左上角创建相同大小的入口和出口区域
    根据地图尺寸动态调整入口和出口区域的大小，并确保入口和出口与真实地图有足够的接触面积
    """
    h, w = grid_map.shape
    
    # 计算扩展区域的大小（取网格尺寸的10%和3的较大值）
    extension_size = max(int(min(h, w) * 0.1), 1)

    # 创建新的扩展地图，包括原始区域、扩展区域和边界
    new_h = h + 2 + 2 * extension_size  # 额外的2是为了边界，2*extension_size是为了入口和出口
    new_w = w + 2 + 2 * extension_size
    new_map = np.ones((new_h, new_w))  # 初始化为边界（1）
    
    # 将原始地图复制到新地图的中心区域
    new_map[extension_size+1:-extension_size-1, extension_size+1:-extension_size-1] = grid_map
    
    # 创建右下角入口扩展区域（对角线形式）
    for i in range(new_h - 2 * extension_size - 1, new_h):
        for j in range(new_w - 2 * extension_size - 1, new_w):
            # 创建对角线形式的入口，只有在对角线附近的点才设为流体
            # 使用相对坐标计算对角线位置
            rel_i = i - (new_h - 2 * extension_size - 1)
            rel_j = j - (new_w - 2 * extension_size - 1)
            if rel_i == rel_j or abs(rel_i - rel_j) <= extension_size // 2:
                new_map[i, j] = 0
    
    # 创建左上角出口扩展区域（对角线形式）
    for i in range(2 * extension_size + 1):
        for j in range(2 * extension_size + 1):
            # 创建对角线形式的出口，只有在对角线附近的点才设为流体
            if i == j or abs(i - j) <= extension_size // 2:
                new_map[i, j] = 0
    
    return new_map

def equilibrium(rho, u, v):
    """
    计算平衡分布函数（向量化版本）
    
    参数:
    rho: 密度场（一维数组）
    u, v: 速度场分量（一维数组）
    """
    # 预计算平方项
    usqr = u**2 + v**2
    
    # 初始化平衡分布函数数组
    feq = np.zeros((9, len(rho)))
    
    # 向量化计算所有方向的平衡分布
    for i in range(9):
        # 计算速度点积 c_i·u
        cu = c[i,0]*u + c[i,1]*v
        # 平衡分布函数公式
        feq[i] = w[i]*rho*(1 + 3*cu + 4.5*cu**2 - 1.5*usqr)
    
    return feq

def bounce_back_optimized(f, grid_map):
    """
    处理障碍物边界的反弹边界条件（优化版本）
    使用预计算的反向索引和布尔掩码进行向量化操作
    """
    # 创建障碍物掩码
    obstacle_mask = (grid_map == 1)
    
    # 对每个方向应用反弹规则
    for i in range(1, 9):  # 跳过静止方向
        opp_i = opposite[i]  # 获取反向索引
        # 在障碍物位置交换分布函数
        temp = f[i].copy()
        f[i, obstacle_mask] = f[opp_i, obstacle_mask]
        f[opp_i, obstacle_mask] = temp[obstacle_mask]
    
    return f

def apply_zou_he_boundary(f, rho, u, v, grid_map, inlet_velocity):
    """
    应用Zou-He边界条件，比简单的速度设置更准确
    
    参数:
    f: 分布函数
    rho, u, v: 宏观量
    grid_map: 网格地图
    inlet_velocity: 入口速度
    """
    nx, ny = grid_map.shape
    
    # 计算扩展区域的大小，与add_boundary函数保持一致
    h, w = grid_map.shape
    extension_size = max(int(min(h, w) * 0.1), 3)
    
    # 入口边界条件（右下角区域）- 速度边界
    # 检测入口位置
    inlet_mask = np.zeros_like(grid_map, dtype=bool)
    inlet_mask[-1, -extension_size-1:] = True  # 下边界
    inlet_mask[-extension_size-1:, -1] = True  # 右边界
    inlet_mask = inlet_mask & (grid_map == 0)  # 只在流体区域应用
    
    # 设置入口速度（对角线方向，从右下角流向左上角）
    u[inlet_mask] = -inlet_velocity / np.sqrt(2)
    v[inlet_mask] = -inlet_velocity / np.sqrt(2)
    
    # 出口边界条件（左上角区域）- 压力边界
    # 检测出口位置
    outlet_mask = np.zeros_like(grid_map, dtype=bool)
    outlet_mask[0, :extension_size+1] = True  # 上边界
    outlet_mask[:extension_size+1, 0] = True  # 左边界
    outlet_mask = outlet_mask & (grid_map == 0)  # 只在流体区域应用
    
    # 设置出口压力（密度）
    rho[outlet_mask] = 1.0
    
    # 零梯度速度条件
    # 对于左边界
    u[:extension_size+1, 0] = u[:extension_size+1, 1]
    v[:extension_size+1, 0] = v[:extension_size+1, 1]
    # 对于上边界
    u[0, :extension_size+1] = u[1, :extension_size+1]
    v[0, :extension_size+1] = v[1, :extension_size+1]
    
    return f, rho, u, v


def simulate_flow(grid_map, max_iter=5000, tau=0.5, inlet_velocity=0.1, convergence_threshold=1e-6, 
                 convergence_window=50, use_multiprocessing=False, num_processes=None):
    """
    使用LBM模拟流动（优化版本）
    
    参数:
    grid_map: 网格地图，0表示流体，1表示障碍物
    max_iter: 最大迭代次数
    tau: 松弛时间，控制粘性
    inlet_velocity: 入口速度
    convergence_threshold: 收敛阈值
    convergence_window: 收敛判断窗口大小
    use_multiprocessing: 是否使用多进程加速
    num_processes: 进程数量，默认为None（使用CPU核心数）
    """
    # 记录开始时间
    start_time = time.time()
    
    # 初始化
    nx, ny = grid_map.shape
    f = np.ones((9, nx, ny)) / 9.0  # 均匀初始化，更稳定
    
    # 添加小的随机扰动以打破对称性
    np.random.seed(42)  # 设置随机种子以保证可重复性
    f += 0.01 * np.random.randn(9, nx, ny) / 9.0
    
    # 初始化宏观量
    rho = np.ones((nx, ny))
    u = np.zeros((nx, ny))
    v = np.zeros((nx, ny))
    
    # 记录收敛历史
    convergence_history = []
    velocity_window = []  # 用于滑动窗口收敛判断
    
    # 设置多进程
    if use_multiprocessing:
        if num_processes is None:
            num_processes = mp.cpu_count()
        print(f"使用多进程加速，进程数: {num_processes}")
        pool = mp.Pool(processes=num_processes)
    
    # 主循环
    for step in range(max_iter):
        # 保存上一步的速度场用于收敛检查
        if step > 0:
            u_old = u.copy()
            v_old = v.copy()
        
        # 计算宏观量
        rho = np.sum(f, axis=0)
        u = np.zeros_like(rho)
        v = np.zeros_like(rho)
        
        # 向量化计算速度
        for i in range(9):
            u += c[i,0] * f[i]
            v += c[i,1] * f[i]
        
        # 防止除零错误
        mask = (rho > 1e-10)
        u[mask] = u[mask] / rho[mask]
        v[mask] = v[mask] / rho[mask]
        
        # 应用边界条件
        f, rho, u, v = apply_zou_he_boundary(f, rho, u, v, grid_map, inlet_velocity)
        
        # 障碍物处的速度设为0（向量化操作）
        obstacle_mask = (grid_map == 1)
        u[obstacle_mask] = 0
        v[obstacle_mask] = 0
        
        # 计算平衡态
        rho_flat = rho.flatten()
        u_flat = u.flatten()
        v_flat = v.flatten()
        
        # 使用多进程计算平衡分布（对大规模问题有效）
        if use_multiprocessing and nx*ny > 10000:
            # 将数据分块
            chunk_size = len(rho_flat) // num_processes
            chunks = [(rho_flat[i:i+chunk_size], u_flat[i:i+chunk_size], v_flat[i:i+chunk_size]) 
                      for i in range(0, len(rho_flat), chunk_size)]
            
            # 并行计算平衡分布
            results = pool.starmap(equilibrium, chunks)
            
            # 合并结果
            feq = np.hstack(results)
        else:
            # 单进程计算
            feq = equilibrium(rho_flat, u_flat, v_flat)
        
        feq = feq.reshape((9, nx, ny))
        
        # 碰撞步骤（向量化）
        f = f - (1/tau)*(f - feq)
        
        # 流动步骤（向量化）
        for i in range(9):
            f[i] = np.roll(np.roll(f[i], c[i,0], axis=0), c[i,1], axis=1)
        
        # 处理障碍物边界
        f = bounce_back_optimized(f, grid_map)
        
        # 检查收敛性
        if step > 0:
            velocity_diff = np.sqrt(np.mean((u - u_old)**2 + (v - v_old)**2))
            convergence_history.append(velocity_diff)
            velocity_window.append(velocity_diff)
            
            # 保持窗口大小
            if len(velocity_window) > convergence_window:
                velocity_window.pop(0)
            
            # 每100步打印一次状态
            if step % 100 == 0:
                elapsed = time.time() - start_time
                print(f"Iteration {step}, Velocity change: {velocity_diff:.8f}, Time: {elapsed:.2f}s")
            
            # 使用滑动窗口判断收敛
            if len(velocity_window) == convergence_window:
                window_mean = np.mean(velocity_window)
                window_std = np.std(velocity_window)
                
                # 如果平均变化小于阈值且标准差小，认为收敛
                if window_mean < convergence_threshold and window_std < convergence_threshold/2 and step > 1000:
                    elapsed = time.time() - start_time
                    print(f"Converged after {step} iterations, Time: {elapsed:.2f}s")
                    break
    
    # 关闭进程池
    if use_multiprocessing:
        pool.close()
        pool.join()
    
    # 计算速度大小和涡量（向量化）
    speed = np.sqrt(u**2 + v**2)
    
    # 计算涡量（使用中心差分，向量化）
    vorticity = np.zeros_like(u)
    # 内部点使用中心差分
    vorticity[1:-1, 1:-1] = (v[2:, 1:-1] - v[:-2, 1:-1]) - (u[1:-1, 2:] - u[1:-1, :-2])
    
    return u, v, rho, speed, vorticity, convergence_history

def calculate_reynolds_number(u_max, characteristic_length, tau):
    """
    计算雷诺数
    
    参数:
    u_max: 最大速度
    characteristic_length: 特征长度（通常为通道宽度）
    tau: 松弛时间
    """
    # LBM中的运动粘度与松弛时间的关系: nu = (tau - 0.5) / 3
    nu = (tau - 0.5) / 3.0
    return u_max * characteristic_length / nu

def load_excel_data(file_path='label/sea_ice_419.xlsx'):
    """
    从Excel文件加载数据并转换为二进制地图
    """
    try:
        import pandas as pd
        data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
        numpy_array = data.to_numpy()
        # 转换为二进制地图：0表示流体，1表示障碍物
        binary_map = np.where(numpy_array == 0, 255, np.where(numpy_array == 255, 0, numpy_array))
        binary_map = binary_map / 255  # 归一化到0-1范围
        binary_map = binary_map.astype(int)  # 转换为整数
        #binary_map = numpy_array.astype(int)  # 转换为整数
        return binary_map
    except Exception as e:
        print(f"Error loading Excel data: {e}")
        return None

def visualize_results(grid_map, u, v, rho, speed, vorticity, convergence_history):
    """
    可视化模拟结果，包括速度场、压力场、涡量场、速度矢量场、流线图和收敛历史
    """
    # 创建自定义颜色映射
    custom_cmap = create_custom_colormap()
    
    # 创建图形
    fig = plt.figure(figsize=(9, 8),dpi=500)
    
    # 1. 速度场
    ax1 = fig.add_subplot(221)
    im1 = ax1.imshow(speed, cmap=custom_cmap,vmin=0,vmax=0.01)
    plt.colorbar(im1, ax=ax1, label='Speed')
    ax1.imshow(grid_map, cmap='gray_r', alpha=0.3)
    ax1.set_title('Velocity Magnitude')
    
    import os
    import pandas as pd
    # 保存为 Excel
    base_path = "C:/Users/<USER>/Desktop/ICE/lable"
    output_excel_path = os.path.join(base_path, "Velocity.xlsx")
    df = pd.DataFrame(speed)
    os.makedirs(os.path.dirname(output_excel_path), exist_ok=True)
    df.to_excel(output_excel_path, index=False, header=False)
    print(f"处理完成，结果已保存至 {output_excel_path}")

    # # 2. 压力场（用密度表示）
    # ax2 = fig.add_subplot(222)
    # im2 = ax2.imshow(rho, cmap='coolwarm',vmin=0.9999, vmax=1)
    # plt.colorbar(im2, ax=ax2, label='Density')
    # ax2.imshow(grid_map, cmap='gray_r', alpha=0.3)
    # ax2.set_title('Pressure Field')
    
    # 3. 涡量场
    ax3 = fig.add_subplot(222)
    # 使用对称的颜色映射，使零值为白色
    im3 = ax3.imshow(vorticity, cmap='seismic', vmin=-0.01, vmax=0.01)
    plt.colorbar(im3, ax=ax3, label='Vorticity')
    ax3.set_title('Vorticity Field')


    # # 4. 速度矢量场
    #ax4 = fig.add_subplot(234)
    skip = max(1, grid_map.shape[0] // 10)  # 动态调整跳过的点数，使图更清晰
    x, y = np.meshgrid(np.arange(0, grid_map.shape[1]), np.arange(0, grid_map.shape[0]))
    # quiver = ax4.quiver(x[::skip, ::skip], y[::skip, ::skip], 
    #                   u[::skip, ::skip], v[::skip, ::skip],
    #                   speed[::skip, ::skip], cmap=custom_cmap, scale=15)
    # plt.colorbar(quiver, ax=ax4, label='Speed')
    # ax4.imshow(grid_map, cmap='gray', alpha=0.3)
    # ax4.set_title('Velocity Vectors')
    
    # 5. 流线图
    ax5 = fig.add_subplot(223)
    streamplot = ax5.streamplot(x, y, u, v, density=1.5, color=speed, cmap=custom_cmap, linewidth=1.0)
    plt.colorbar(streamplot.lines, ax=ax5, label='Speed')
    ax5.imshow(grid_map, cmap='gray', alpha=0.3)
    ax5.set_title('Streamlines')
    
    # 6. 收敛历史
    ax6 = fig.add_subplot(224)
    ax6.semilogy(convergence_history)
    ax6.set_xlabel('Iteration')
    ax6.set_ylabel('Velocity Change (log scale)')
    ax6.set_title('Convergence History')
    ax6.grid(True)
    
    plt.tight_layout()
    return fig


def modify_grid_map(grid_map):
    new_grid_map = grid_map.copy()
    nrows, ncols = new_grid_map.shape

    # -------------------------
    # 1. 左上角区域处理
    # 在第一行中从左向右寻找第一个非0元素
    first_row = new_grid_map[0, :]
    nonzero_cols = np.nonzero(first_row)[0]
    if len(nonzero_cols) > 0:
        j = nonzero_cols[0]
    else:
        j = ncols  # 若无非0，则取数组宽度

    # 在第一列中从上向下寻找第一个非0元素
    first_col = new_grid_map[:, 0]
    nonzero_rows = np.nonzero(first_col)[0]
    if len(nonzero_rows) > 0:
        i = nonzero_rows[0]
    else:
        i = nrows  # 若无非0，则取数组高度

    # 如果 i 和 j 均大于0，则对满足条件的区域赋值
    if i > 0 and j > 0:
        for r in range(nrows):
            for c in range(ncols):
                # 连线方程： r/i + c/j = 1
                # 连线以上（左上角区域）即满足： r/i + c/j < 1
                if (r / i + c / j) < 1:
                    new_grid_map[r, c] = 1

    # -------------------------
    # 2. 右下角区域处理
    # 在最后一行中从右向左寻找第一个非0元素
    last_row = new_grid_map[nrows - 1, :]
    nonzero_cols_br = np.nonzero(last_row)[0]
    if len(nonzero_cols_br) > 0:
        # 从右开始，第一个非0即为非0元素中最大的索引
        col_br = nonzero_cols_br[-1]
    else:
        col_br = 0  # 或按需求处理

    # 在最后一列中从下向上寻找第一个非0元素
    last_col = new_grid_map[:, ncols - 1]
    nonzero_rows_br = np.nonzero(last_col)[0]
    if len(nonzero_rows_br) > 0:
        row_br = nonzero_rows_br[-1]
    else:
        row_br = 0

    # 如果 (nrows-1 - row_br) 和 (ncols-1 - col_br) 非0，则进行区域赋值
    if (nrows - 1 - row_br) > 0 and (ncols - 1 - col_br) > 0:
        for r in range(nrows):
            for c in range(ncols):
                # 坐标变换： r' = nrows-1 - r, c' = ncols-1 - c
                # 连线方程转换后为： r'/(nrows-1 - row_br) + c'/(ncols-1 - col_br) = 1
                # 连线以下（右下角区域）即满足： r'/(nrows-1 - row_br) + c'/(ncols-1 - col_br) < 1
                if ((nrows - 1 - r) / (nrows - 1 - row_br) + (ncols - 1 - c) / (ncols - 1 - col_br)) < 1:
                    new_grid_map[r, c] = 1

    return new_grid_map

def smooth_path(path, grid_map):
    """
    使用贝塞尔曲线对路径进行平滑处理
    
    参数:
    path: 原始路径坐标列表
    grid_map: 网格地图，0表示流体，1表示障碍物
    
    返回:
    smoothed_path: 平滑后的路径坐标列表
    """
    if not path or len(path) < 3:
        return path
    
    def bezier_curve(p0, p1, p2, steps=10):
        """计算二次贝塞尔曲线上的点"""
        points = []
        for t in range(steps + 1):
            t = t / steps
            x = (1-t)**2 * p0[0] + 2*(1-t)*t * p1[0] + t**2 * p2[0]
            y = (1-t)**2 * p0[1] + 2*(1-t)*t * p1[1] + t**2 * p2[1]
            points.append((int(round(x)), int(round(y))))
        return points
    
    def is_valid_point(point, grid_map):
        """检查点是否在流体区域内"""
        x, y = point
        return (0 <= x < grid_map.shape[0] and 
                0 <= y < grid_map.shape[1] and 
                grid_map[x, y] == 0)
    
    def get_control_point(p0, p1, p2, grid_map):
        """计算控制点，确保路径平滑且有效"""
        # 计算中点
        mid_x = (p0[0] + p2[0]) / 2
        mid_y = (p0[1] + p2[1]) / 2
        
        # 计算控制点（在中点基础上稍微偏移）
        dx = p1[0] - mid_x
        dy = p1[1] - mid_y
        
        # 尝试不同的偏移量，找到最合适的控制点
        for scale in [0.5, 0.3, 0.1]:
            control_x = int(round(mid_x + dx * scale))
            control_y = int(round(mid_y + dy * scale))
            if is_valid_point((control_x, control_y), grid_map):
                return (control_x, control_y)
        
        # 如果都无效，返回中点
        return (int(round(mid_x)), int(round(mid_y)))
    
    smoothed_path = []
    i = 0
    
    while i < len(path):
        if i == 0:
            # 处理起点
            smoothed_path.append(path[0])
            i += 1
            continue
        
        if i == len(path) - 1:
            # 处理终点
            smoothed_path.append(path[-1])
            break
        
        # 获取当前段的三点
        p0 = path[i-1]
        p1 = path[i]
        p2 = path[i+1]
        
        # 计算控制点
        control = get_control_point(p0, p1, p2, grid_map)
        
        # 生成贝塞尔曲线上的点
        curve_points = bezier_curve(p0, control, p2)
        
        # 验证并添加有效的点
        for point in curve_points:
            if is_valid_point(point, grid_map):
                smoothed_path.append(point)
        
        i += 1
    
    # 后处理：移除冗余点
    final_path = []
    for i in range(len(smoothed_path)):
        if i == 0 or i == len(smoothed_path) - 1:
            final_path.append(smoothed_path[i])
        else:
            # 检查当前点是否与前一个点不同
            if smoothed_path[i] != smoothed_path[i-1]:
                final_path.append(smoothed_path[i])
    
    return final_path

def find_optimal_path(grid_map, speed, u, v):
    """
    基于速度场寻找从出口到入口的最佳路径
    
    参数:
    grid_map: 网格地图，0表示流体，1表示障碍物
    speed: 速度大小场
    u, v: 速度分量场
    
    返回:
    path: 最佳路径坐标列表，从出口到入口
    """
    # 确定网格大小
    h, w = grid_map.shape
    
    # 计算扩展区域的大小（与add_boundary保持一致）
    extension_size = max(int(min(h, w) * 0.1), 1)
    
    # 找到出口区域的中心点作为起点
    inlet_center = None
    for i in range(extension_size + 1):
        for j in range(extension_size + 1):
            if grid_map[i, j] == 0 and i == j:
                inlet_center = (i, j)
                break
        if inlet_center:
            break
    
    if not inlet_center:
        # 如果没有找到确切的对角线点，使用左上角的第一个流体点
        for i in range(extension_size + 1):
            for j in range(extension_size + 1):
                if grid_map[i, j] == 0:
                    inlet_center = (i, j)
                    break
            if inlet_center:
                break
    

    # 找到入口区域的中心点作为终点
    outlet_center = None
    for i in range(h - extension_size - 1, h):
        for j in range(w - extension_size - 1, w):
            if grid_map[i, j] == 0 and (i - (h - extension_size - 1)) == (j - (w - extension_size - 1)):
                outlet_center = (i, j)
                break
        if outlet_center:
            break
    
    if not outlet_center:
        # 如果没有找到确切的对角线点，使用右下角的第一个流体点
        for i in range(h - 1, h - extension_size - 2, -1):
            for j in range(w - 1, w - extension_size - 2, -1):
                if grid_map[i, j] == 0:
                    outlet_center = (i, j)
                    break
            if outlet_center:
                break
    
    print(f"出口中心点: {outlet_center}, 入口中心点: {inlet_center}")
    
    # 平滑速度场以减少局部噪声对路径规划的影响
    smooth_speed = gaussian_filter(speed, sigma=1.0)
    smooth_u = gaussian_filter(u, sigma=1.0)
    smooth_v = gaussian_filter(v, sigma=1.0)
    
    # 为了避免在障碍物附近选择路径，增加障碍物周围区域的权重
    weight_map = np.ones_like(smooth_speed)
    obstacle_mask = (grid_map == 1)
    
    # 扩展障碍物影响区域
    for i in range(1, h-1):
        for j in range(1, w-1):
            if obstacle_mask[i, j]:
                for di in [-1, 0, 1]:
                    for dj in [-1, 0, 1]:
                        ni, nj = i+di, j+dj
                        if 0 <= ni < h and 0 <= nj < w and not obstacle_mask[ni, nj]:
                            weight_map[ni, nj] = 5.0  # 增加障碍物邻近区域的权重
    
    # 计算流体方向与移动方向的一致性
    # 从出口到入口的路径应该尽量顺着流体方向
    
    # 可能的移动方向（8个方向）
    directions = [(-1, 0), (1, 0), (0, -1), (0, 1), (-1, -1), (-1, 1), (1, -1), (1, 1)]
    
    # 创建方向权重矩阵
    direction_weight = np.ones_like(smooth_speed) * 5.0  # 默认权重
    
    # 对于每个流体单元，计算流动方向与各个移动方向的一致性
    for i in range(h):
        for j in range(w):
            if grid_map[i, j] == 0:  # 只处理流体单元
                # 获取当前位置的流体速度分量
                u_val = smooth_u[i, j]
                v_val = smooth_v[i, j]
                
                # 如果速度太小，则不考虑方向
                if abs(u_val) < 1e-6 and abs(v_val) < 1e-6:
                    continue
                
                # 流体速度向量归一化
                mag = np.sqrt(u_val**2 + v_val**2)
                if mag > 0:
                    u_norm = u_val / mag
                    v_norm = v_val / mag
                    
                    # 对于每个可能的移动方向，计算与流体方向的一致性
                    # 这里使用点积，范围从-1（完全相反）到1（完全一致）
                    for dx, dy in directions:
                        # 移动方向归一化
                        move_mag = np.sqrt(dx**2 + dy**2)
                        dx_norm = dx / move_mag
                        dy_norm = dy / move_mag
                        
                        # 计算点积（方向一致性）
                        # 注意我们要从出口到入口，所以实际上要逆着流体方向走
                        # 因此点积取反
                        dot_product = -(dx_norm * u_norm + dy_norm * v_norm)
                        
                        # 转换为权重：dot_product范围是[-1, 1]
                        # 当方向与流体方向一致时（dot_product接近1），权重小
                        # 当方向与流体方向相反时（dot_product接近-1），权重大
                        # 将点积映射到权重范围[1, 10]
                        weight = 5.5 - 4.5 * dot_product  # 范围从1（完全一致）到10（完全相反）
                        
                        # 更新方向权重
                        ni, nj = i + dx, j + dy
                        if 0 <= ni < h and 0 <= nj < w:
                            direction_weight[i, j] = min(direction_weight[i, j], weight)
    
    # 创建最终的代价函数：结合速度大小、障碍物权重和方向一致性
    # 速度越大代价越小，更倾向于选择高速区域
    cost_map = weight_map * direction_weight / (smooth_speed + 1e-6)
    
    # A*算法实现寻找最佳路径
    # 启发式函数：到目标的曼哈顿距离
    def heuristic(a, b):
        return abs(a[0] - b[0]) + abs(a[1] - b[1])
    
    # 初始化开放集和关闭集
    open_set = []
    closed_set = set()
    came_from = {}
    
    # g_score表示从起点到当前点的实际代价
    g_score = {outlet_center: 0}
    # f_score表示估计从起点经过当前点到终点的总代价
    f_score = {outlet_center: heuristic(outlet_center, inlet_center)}
    
    # 使用优先队列实现开放集，按f_score排序
    heapq.heappush(open_set, (f_score[outlet_center], outlet_center))
    
    # 用于跟踪优先队列中的节点
    in_open_set = {outlet_center}
    
    while open_set:
        _, current = heapq.heappop(open_set)
        in_open_set.remove(current)
        
        if current == inlet_center:
            # 找到路径，重建路径
            path = [current]
            while current in came_from:
                current = came_from[current]
                path.append(current)
            # 对路径进行平滑处理
            smoothed_path = smooth_path(path, grid_map)
            return smoothed_path  # 路径从终点到起点，需要反转
        
        closed_set.add(current)
        
        for dx, dy in directions:
            neighbor = (current[0] + dx, current[1] + dy)
            
            # 检查边界
            if not (0 <= neighbor[0] < h and 0 <= neighbor[1] < w):
                continue
            
            # 检查是否是障碍物
            if grid_map[neighbor[0], neighbor[1]] == 1:
                continue
            
            # 检查是否已经在关闭集中
            if neighbor in closed_set:
                continue
            
            # 计算从起点到邻居的临时g_score
            # 对角线移动距离为sqrt(2)≈1.414，直线移动为1
            move_cost = 1.414 if dx != 0 and dy != 0 else 1.0
            
            # 获取当前单元格和邻居之间的平均代价
            current_cost = cost_map[current[0], current[1]]
            neighbor_cost = cost_map[neighbor[0], neighbor[1]]
            avg_cost = (current_cost + neighbor_cost) / 2
            
            tentative_g_score = g_score[current] + move_cost * avg_cost
            
            # 检查邻居是否已经在开放集中，或者找到了更好的路径
            if neighbor not in in_open_set or tentative_g_score < g_score.get(neighbor, float('inf')):
                # 这条路径更好，记录下来
                came_from[neighbor] = current
                g_score[neighbor] = tentative_g_score
                f_score[neighbor] = tentative_g_score + heuristic(neighbor, inlet_center)
                
                # 如果邻居不在开放集中，添加它
                if neighbor not in in_open_set:
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
                    in_open_set.add(neighbor)
    
    # 如果没有找到路径
    print("未找到从出口到入口的路径！")
    return None

def visualize_path(grid_map, speed, path, u, v):
    """
    可视化最佳路径
    
    参数:
    grid_map: 网格地图
    speed: 速度场
    path: 从出口到入口的路径
    u, v: 速度分量
    """
    # 创建自定义颜色映射
    custom_cmap = create_custom_colormap()
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 8), dpi=300)
    
    # 绘制速度场
    im = ax.imshow(speed, cmap=custom_cmap, vmin=0, vmax=np.percentile(speed, 95), alpha=0.3)
    plt.colorbar(im, ax=ax, label='Speed')
    
    # 绘制障碍物
    ax.imshow(grid_map, cmap='gray_r', alpha=0.3)
    
    # 绘制流线作为背景
    h, w = grid_map.shape
    x, y = np.meshgrid(np.arange(0, w), np.arange(0, h))
    streamplot = ax.streamplot(x, y, u, v, density=1.0, color='gray', linewidth=0.5, arrowsize=0.5)
    
    # 绘制路径
    if path:
        path_x = [p[1] for p in path]  # 注意：交换坐标，plt 中 x 对应列
        path_y = [p[0] for p in path]  # y 对应行
        ax.plot(path_x, path_y, 'r-', linewidth=2.5, label='Optimal Path')
        
        # 标记起点和终点
        ax.plot(path_x[-1], path_y[-1], 'go', markersize=8, label='Start (Outlet)')
        ax.plot(path_x[0], path_y[0], 'bo', markersize=8, label='End (Inlet)')
    
    ax.set_title('Optimal Path from Outlet to Inlet')
    ax.legend(loc='upper right')
    
    plt.tight_layout()
    return fig

def main():
    """
    主函数：加载数据，运行模拟，可视化结果
    """
    # 记录总运行时间
    total_start_time = time.time()
    
    # 尝试从Excel加载数据
    excel_data = load_excel_data()
    
    if excel_data is not None:
        grid_map = excel_data
        print("Using data from Excel file")
    else:
        # 使用示例网格地图
        grid_map = np.array([
            [0, 0, 0, 0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0, 1, 0, 0],
            [0, 1, 0, 0, 1, 0, 1, 0],
            [0, 1, 1, 1, 0, 0, 0, 0],
            [0, 0, 0, 1, 0, 1, 1, 0],
            [0, 1, 0, 0, 0, 0, 0, 0],
            [0, 0, 1, 1, 0, 1, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0]
        ])
        print("Using example grid map")
    
    # 添加边界
    grid_map = add_boundary(grid_map)
    #grid_map = modify_grid_map(grid_map.copy())
    
    # 显示原始地图
    plt.figure(figsize=(8, 8))
    plt.imshow(grid_map, cmap='gray')
    plt.title("Grid Map with Boundary")
    plt.colorbar(label='Obstacle (1) / Fluid (0)')
    plt.show()
    
    print("Starting LBM simulation...")
    print(f"Grid size: {grid_map.shape[0]}x{grid_map.shape[1]}")
    
    # 设置模拟参数
    tau = 1  # 松弛时间
    inlet_velocity =0.8 # 入口速度
    max_iter = 20000  # 最大迭代次数
    convergence_threshold = 1e-10 # 收敛阈值
    
    # 检测是否可以使用多进程
    use_multiprocessing = False
    if grid_map.size > 10000:  # 对于大规模问题，考虑使用多进程
        use_multiprocessing = True
        print("Large grid detected, enabling multiprocessing")
    
    # 模拟流动
    u, v, rho, speed, vorticity, convergence_history = simulate_flow(
        grid_map, 
        max_iter=max_iter, 
        tau=tau, 
        inlet_velocity=inlet_velocity, 
        convergence_threshold=convergence_threshold,
        use_multiprocessing=use_multiprocessing
    )
    
    # 计算雷诺数
    characteristic_length = min(grid_map.shape) / 2  # 使用网格尺寸的一半作为特征长度
    re = calculate_reynolds_number(np.max(speed), characteristic_length, tau)
    print(f"Estimated Reynolds number: {re:.2f}")
    
    # 可视化模拟结果
    fig = visualize_results(grid_map, u, v, rho, speed, vorticity, convergence_history)
    
    # 寻找最佳路径
    print("寻找从出口到入口的最佳路径...")
    optimal_path = find_optimal_path(grid_map, speed, u, v)
    
    if optimal_path:
        # 可视化路径
        path_fig = visualize_path(grid_map, speed, optimal_path, u, v)
        
        # 保存路径图像
        path_fig.savefig('optimal_path.png', dpi=300, bbox_inches='tight')
        
        # 计算路径长度和平均速度
        path_length = len(optimal_path)
        path_speeds = [speed[p[0], p[1]] for p in optimal_path]
        avg_speed = np.mean(path_speeds)
        
        print(f"找到最佳路径，长度: {path_length} 个单元格")
        print(f"路径上的平均速度: {avg_speed:.6f}")
        
        # 显示路径图像
        plt.figure(figsize=(10, 8))
        plt.show()
    
    # 显示结果图像
    plt.show()
    
    # 打印总运行时间
    total_elapsed = time.time() - total_start_time
    print(f"Total execution time: {total_elapsed:.2f} seconds")

if __name__ == "__main__":
    main()