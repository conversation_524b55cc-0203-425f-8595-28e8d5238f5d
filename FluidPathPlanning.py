"""
优化的流体路径规划系统 v2.0
基于格子玻尔兹曼方法(LBM)的流体模拟和多种路径规划算法的集成系统

主要特性:
1. 模块化设计，代码结构清晰
2. 改进的LBM实现，支持多松弛时间模型
3. 多种路径规划算法比较
4. 全面的可视化功能
5. 性能分析和指标计算

作者: AI Assistant
版本: 2.0
"""

import numpy as np
import matplotlib.pyplot as plt
import time

# 设置matplotlib字体为Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12

# 导入自定义模块
try:
    from fluid_path_planner import FluidPathPlanner
    from utils import DataProcessor, GridProcessor
    print("成功导入所有模块")
except ImportError as e:
    print(f"模块导入失败: {e}")
    print("请确保所有模块文件都在同一目录下")
    exit(1)

def run_comprehensive_demo():
    """
    运行全面的演示程序，展示所有功能
    """
    print("=" * 60)
    print("流体路径规划系统 - 全面演示")
    print("=" * 60)

    # 创建示例场景
    scenarios = create_example_scenarios()

    # 选择场景
    print("\n可用场景:")
    for i, (name, info) in enumerate(scenarios.items(), 1):
        print(f"{i}. {name}: {info['description']}")

    # 使用简单场景进行演示
    selected_scenario = 'simple'
    grid_map = scenarios[selected_scenario]['grid']

    print(f"\n使用场景: {scenarios[selected_scenario]['description']}")
    print(f"网格大小: {grid_map.shape}")

    # 创建流体路径规划器
    planner = FluidPathPlanner(grid_map)

    # 准备网格
    planner.prepare_grid_with_inlet_outlet(boundary_type='diagonal')

    # 可视化初始网格
    print("\n可视化初始网格...")
    grid_fig = planner.visualizer.visualize_grid_with_boundary(
        planner.grid_map, planner.start_point, planner.end_point)
    plt.show()

    # 运行流体模拟
    print("\n开始流体模拟...")
    planner.run_fluid_simulation(
        max_iter=3000,
        tau=0.8,
        inlet_velocity=0.1,
        collision_model='bgk',
        boundary_condition='zou_he'
    )

    # 可视化流体场
    print("\n可视化流体场...")
    fluid_fig = planner.visualize_fluid_fields()
    plt.show()

    # 3D可视化
    print("\n3D可视化速度场...")
    speed_3d_fig = planner.visualize_3d_fields('speed')
    plt.show()

    # 比较所有路径规划算法
    print("\n比较所有路径规划算法...")
    paths, metrics = planner.compare_all_paths()

    # 可视化路径比较
    print("\n可视化路径比较...")
    path_fig = planner.visualize_paths()
    plt.show()

    # 显示性能指标
    print("\n性能指标比较:")
    perf_fig = planner.visualizer.plot_performance_metrics(metrics)
    plt.show()

    # 保存结果
    print("\n保存结果...")
    planner.save_results("demo_results")

    # 显示性能总结
    summary = planner.get_performance_summary()
    print("\n性能总结:")
    print(f"网格大小: {summary['grid_size']}")
    print(f"测试算法数量: {summary['total_paths_found']}")
    print(f"找到的路径: {summary['algorithms_tested']}")

    print("\n演示完成！")
    return planner


def create_example_scenarios():
    """
    创建示例场景用于演示

    返回:
    场景字典
    """
    scenarios = {}

    # 场景1: 简单障碍物
    simple_grid = np.zeros((20, 30), dtype=int)
    simple_grid[8:12, 10:20] = 1  # 矩形障碍物
    scenarios['simple'] = {
        'grid': simple_grid,
        'description': '简单矩形障碍物场景'
    }

    # 场景2: 复杂迷宫
    maze_grid = np.zeros((25, 25), dtype=int)
    # 创建迷宫结构
    maze_grid[5:20, 5] = 1
    maze_grid[5:20, 15] = 1
    maze_grid[5, 5:15] = 1
    maze_grid[15, 10:20] = 1
    maze_grid[10, 8:12] = 1
    scenarios['maze'] = {
        'grid': maze_grid,
        'description': '迷宫场景'
    }

    # 场景3: 多障碍物
    multi_grid = np.zeros((30, 40), dtype=int)
    # 添加多个圆形障碍物
    centers = [(8, 10), (15, 25), (22, 15), (10, 30)]
    for cx, cy in centers:
        for i in range(multi_grid.shape[0]):
            for j in range(multi_grid.shape[1]):
                if (i - cx)**2 + (j - cy)**2 <= 16:  # 半径4的圆
                    multi_grid[i, j] = 1
    scenarios['multi_obstacles'] = {
        'grid': multi_grid,
        'description': '多障碍物场景'
    }

    return scenarios


def load_and_run_excel_data(file_path: str = 'label/sea_ice_281.xlsx'):
    """
    加载Excel数据并运行分析

    参数:
    file_path: Excel文件路径
    """
    print(f"尝试加载Excel数据: {file_path}")

    # 加载数据
    grid_data = DataProcessor.load_excel_data(file_path)

    if grid_data is not None:
        print(f"成功加载数据，大小: {grid_data.shape}")

        # 创建规划器
        planner = FluidPathPlanner(grid_data)

        # 准备网格
        planner.prepare_grid_with_inlet_outlet()

        # 运行流体模拟
        planner.run_fluid_simulation(
            max_iter=5000,
            tau=1.0,
            inlet_velocity=0.8,
            convergence_threshold=1e-10
        )

        # 可视化结果
        fluid_fig = planner.visualize_fluid_fields()
        plt.show()

        # 运行路径规划
        paths, metrics = planner.compare_all_paths()

        # 可视化路径
        path_fig = planner.visualize_paths()
        plt.show()

        # 保存结果
        planner.save_results("excel_results")

        return planner
    else:
        print("无法加载Excel数据，使用示例数据")
        return run_comprehensive_demo()

def parse_arguments():
    """
    解析命令行参数

    返回:
    解析后的参数
    """
    import argparse

    parser = argparse.ArgumentParser(description='流体路径规划系统')
    parser.add_argument('--mode', choices=['demo', 'excel', 'custom'], default='demo',
                       help='运行模式: demo(演示), excel(Excel数据), custom(自定义)')
    parser.add_argument('--excel-path', type=str, default='label/sea_ice_281.xlsx',
                       help='Excel文件路径')
    parser.add_argument('--scenario', choices=['simple', 'maze', 'multi_obstacles'],
                       default='simple', help='演示场景选择')
    parser.add_argument('--max-iter', type=int, default=3000,
                       help='LBM最大迭代次数')
    parser.add_argument('--tau', type=float, default=0.8,
                       help='LBM松弛时间')
    parser.add_argument('--inlet-velocity', type=float, default=0.1,
                       help='入口速度')
    parser.add_argument('--save-results', action='store_true',
                       help='保存结果到文件')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='结果输出目录')

    return parser.parse_args()


def main():
    """
    主函数 - 程序入口点
    """
    print("=" * 60)
    print("流体路径规划系统 v2.0")
    print("基于格子玻尔兹曼方法的智能路径规划")
    print("=" * 60)

    # 解析命令行参数
    args = parse_arguments()

    try:
        if args.mode == 'demo':
            print("运行演示模式...")
            planner = run_comprehensive_demo()

        elif args.mode == 'excel':
            print(f"运行Excel数据模式，文件: {args.excel_path}")
            planner = load_and_run_excel_data(args.excel_path)

        elif args.mode == 'custom':
            print("运行自定义模式...")
            scenarios = create_example_scenarios()

            if args.scenario in scenarios:
                grid_map = scenarios[args.scenario]['grid']
                print(f"使用场景: {scenarios[args.scenario]['description']}")

                # 创建规划器
                planner = FluidPathPlanner(grid_map)
                planner.prepare_grid_with_inlet_outlet()

                # 运行流体模拟
                planner.run_fluid_simulation(
                    max_iter=args.max_iter,
                    tau=args.tau,
                    inlet_velocity=args.inlet_velocity
                )

                # 比较路径
                paths, metrics = planner.compare_all_paths()

                # 可视化
                planner.visualize_fluid_fields()
                planner.visualize_paths()
                plt.show()

                if args.save_results:
                    planner.save_results(args.output_dir)
            else:
                print(f"未知场景: {args.scenario}")
                return

        print("\n程序执行完成！")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()


# 兼容性函数 - 保持与旧版本的兼容性
def add_boundary(grid_map):
    """兼容性函数"""
    return GridProcessor.add_boundary(grid_map)


def load_excel_data(file_path='label/sea_ice_281.xlsx'):
    """兼容性函数"""
    return DataProcessor.load_excel_data(file_path)

# 程序入口点
if __name__ == "__main__":
    main()