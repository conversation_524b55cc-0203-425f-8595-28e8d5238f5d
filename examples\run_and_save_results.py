"""
运行流体路径规划系统并将结果保存到result文件夹
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import time

# 设置matplotlib字体
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12

# 导入自定义模块
try:
    from fluid_path_planner import FluidPathPlanner
    from utils import DataProcessor, GridProcessor
    print("✓ 成功导入所有模块")
except ImportError as e:
    print(f"✗ 模块导入失败: {e}")
    exit(1)


def create_test_scenarios():
    """创建多个测试场景"""
    scenarios = {}
    
    # 场景1: 简单障碍物
    simple_grid = np.zeros((20, 30), dtype=int)
    simple_grid[8:12, 10:20] = 1  # 矩形障碍物
    scenarios['simple_obstacle'] = {
        'grid': simple_grid,
        'description': '简单矩形障碍物场景',
        'params': {'max_iter': 2000, 'tau': 0.8, 'inlet_velocity': 0.1}
    }
    
    # 场景2: 复杂迷宫
    maze_grid = np.zeros((25, 25), dtype=int)
    # 创建迷宫结构
    maze_grid[5:20, 5] = 1
    maze_grid[5:20, 15] = 1
    maze_grid[5, 5:15] = 1
    maze_grid[15, 10:20] = 1
    maze_grid[10, 8:12] = 1
    scenarios['maze'] = {
        'grid': maze_grid,
        'description': '迷宫场景',
        'params': {'max_iter': 3000, 'tau': 0.9, 'inlet_velocity': 0.08}
    }
    
    # 场景3: 多障碍物
    multi_grid = np.zeros((30, 40), dtype=int)
    # 添加多个圆形障碍物
    centers = [(8, 10), (15, 25), (22, 15), (10, 30)]
    for cx, cy in centers:
        for i in range(multi_grid.shape[0]):
            for j in range(multi_grid.shape[1]):
                if (i - cx)**2 + (j - cy)**2 <= 16:  # 半径4的圆
                    multi_grid[i, j] = 1
    scenarios['multi_obstacles'] = {
        'grid': multi_grid,
        'description': '多障碍物场景',
        'params': {'max_iter': 2500, 'tau': 0.7, 'inlet_velocity': 0.12}
    }
    
    return scenarios


def run_scenario(scenario_name, scenario_data, result_dir):
    """运行单个场景并保存结果"""
    print(f"\n{'='*60}")
    print(f"运行场景: {scenario_name}")
    print(f"描述: {scenario_data['description']}")
    print(f"{'='*60}")
    
    # 创建场景专用文件夹
    scenario_dir = os.path.join(result_dir, scenario_name)
    os.makedirs(scenario_dir, exist_ok=True)
    
    # 创建流体路径规划器
    grid_map = scenario_data['grid']
    planner = FluidPathPlanner(grid_map)
    
    print(f"原始网格大小: {grid_map.shape}")
    
    # 准备网格
    planner.prepare_grid_with_inlet_outlet(boundary_type='diagonal')
    print(f"扩展后网格大小: {planner.grid_map.shape}")
    print(f"入口位置: {planner.start_point}")
    print(f"出口位置: {planner.end_point}")
    
    # 保存初始网格图
    try:
        grid_fig = planner.visualizer.visualize_grid_with_boundary(
            planner.grid_map, planner.start_point, planner.end_point,
            save_path=os.path.join(scenario_dir, f"{scenario_name}_initial_grid.png"))
        plt.close(grid_fig)
        print("✓ 初始网格图已保存")
    except Exception as e:
        print(f"✗ 初始网格图保存失败: {e}")
    
    # 运行流体模拟
    params = scenario_data['params']
    print(f"\n开始流体模拟...")
    print(f"参数: {params}")
    
    try:
        start_time = time.time()
        planner.run_fluid_simulation(
            max_iter=params['max_iter'],
            tau=params['tau'],
            inlet_velocity=params['inlet_velocity'],
            convergence_threshold=1e-6,
            collision_model='bgk'
        )
        simulation_time = time.time() - start_time
        print(f"✓ 流体模拟完成，耗时: {simulation_time:.2f}秒")
    except Exception as e:
        print(f"✗ 流体模拟失败: {e}")
        return None
    
    # 保存流体场可视化
    try:
        fluid_fig = planner.visualize_fluid_fields(
            save_path=os.path.join(scenario_dir, f"{scenario_name}_fluid_fields.png"))
        plt.close(fluid_fig)
        print("✓ 流体场可视化已保存")
    except Exception as e:
        print(f"✗ 流体场可视化保存失败: {e}")
    
    # 保存3D可视化
    try:
        speed_3d_fig = planner.visualize_3d_fields('speed')
        speed_3d_fig.savefig(os.path.join(scenario_dir, f"{scenario_name}_3d_speed.png"), 
                            dpi=300, bbox_inches='tight')
        plt.close(speed_3d_fig)
        print("✓ 3D速度场可视化已保存")
    except Exception as e:
        print(f"✗ 3D可视化保存失败: {e}")
    
    # 运行所有路径规划算法
    print(f"\n运行路径规划算法...")
    try:
        paths, metrics = planner.compare_all_paths()
        print(f"✓ 找到 {len(paths)} 条路径")
        
        # 打印路径信息
        for name, path in paths.items():
            if path:
                print(f"  {name}: 长度 {len(path)}")
    except Exception as e:
        print(f"✗ 路径规划失败: {e}")
        paths, metrics = {}, {}
    
    # 保存路径比较可视化
    if paths:
        try:
            path_fig = planner.visualize_paths(
                save_path=os.path.join(scenario_dir, f"{scenario_name}_path_comparison.png"))
            plt.close(path_fig)
            print("✓ 路径比较可视化已保存")
        except Exception as e:
            print(f"✗ 路径比较可视化保存失败: {e}")
    
    # 保存性能指标图表
    if metrics:
        try:
            perf_fig = planner.visualizer.plot_performance_metrics(
                metrics, save_path=os.path.join(scenario_dir, f"{scenario_name}_performance.png"))
            plt.close(perf_fig)
            print("✓ 性能指标图表已保存")
        except Exception as e:
            print(f"✗ 性能指标图表保存失败: {e}")
    
    # 保存数据文件
    try:
        planner.save_results(scenario_dir)
        print("✓ 数据文件已保存")
    except Exception as e:
        print(f"✗ 数据文件保存失败: {e}")
    
    # 保存性能总结
    try:
        summary = planner.get_performance_summary()
        summary_file = os.path.join(scenario_dir, f"{scenario_name}_summary.txt")
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"场景: {scenario_name}\n")
            f.write(f"描述: {scenario_data['description']}\n")
            f.write(f"网格大小: {summary['grid_size']}\n")
            f.write(f"入口位置: {summary['inlet_position']}\n")
            f.write(f"出口位置: {summary['outlet_position']}\n")
            f.write(f"测试算法: {summary['algorithms_tested']}\n")
            f.write(f"找到路径数: {summary['total_paths_found']}\n")
            f.write(f"模拟时间: {simulation_time:.2f}秒\n\n")
            
            f.write("算法性能指标:\n")
            for alg, alg_metrics in summary['performance_metrics'].items():
                f.write(f"\n{alg}:\n")
                for metric, value in alg_metrics.items():
                    if isinstance(value, float):
                        f.write(f"  {metric}: {value:.6f}\n")
                    else:
                        f.write(f"  {metric}: {value}\n")
        
        print("✓ 性能总结已保存")
    except Exception as e:
        print(f"✗ 性能总结保存失败: {e}")
    
    print(f"\n场景 {scenario_name} 完成！")
    return planner


def main():
    """主函数"""
    print("流体路径规划系统 - 批量测试和结果保存")
    print("="*60)
    
    # 创建结果目录
    result_dir = "result"
    os.makedirs(result_dir, exist_ok=True)
    
    # 创建测试场景
    scenarios = create_test_scenarios()
    
    print(f"将运行 {len(scenarios)} 个测试场景:")
    for name, data in scenarios.items():
        print(f"  - {name}: {data['description']}")
    
    # 运行所有场景
    total_start_time = time.time()
    successful_runs = 0
    
    for scenario_name, scenario_data in scenarios.items():
        try:
            planner = run_scenario(scenario_name, scenario_data, result_dir)
            if planner is not None:
                successful_runs += 1
        except Exception as e:
            print(f"✗ 场景 {scenario_name} 运行失败: {e}")
    
    total_time = time.time() - total_start_time
    
    # 创建总体报告
    report_file = os.path.join(result_dir, "overall_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("流体路径规划系统 - 测试报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总运行时间: {total_time:.2f}秒\n")
        f.write(f"测试场景数: {len(scenarios)}\n")
        f.write(f"成功运行: {successful_runs}\n")
        f.write(f"成功率: {successful_runs/len(scenarios)*100:.1f}%\n\n")
        
        f.write("场景列表:\n")
        for name, data in scenarios.items():
            f.write(f"  - {name}: {data['description']}\n")
        
        f.write(f"\n结果保存在: {os.path.abspath(result_dir)}\n")
    
    print(f"\n{'='*60}")
    print(f"所有测试完成！")
    print(f"总运行时间: {total_time:.2f}秒")
    print(f"成功运行: {successful_runs}/{len(scenarios)} 个场景")
    print(f"结果保存在: {os.path.abspath(result_dir)}")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
