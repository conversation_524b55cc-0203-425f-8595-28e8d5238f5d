"""
流体路径规划系统演示脚本
展示新的模块化系统的功能
"""

import numpy as np
import matplotlib.pyplot as plt
import time

# 设置matplotlib字体
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12

# 导入自定义模块
try:
    from fluid_path_planner import FluidPathPlanner
    from utils import DataProcessor, GridProcessor
    print("✓ 成功导入所有模块")
except ImportError as e:
    print(f"✗ 模块导入失败: {e}")
    print("请确保所有模块文件都在同一目录下")
    exit(1)


def create_simple_scenario():
    """创建简单的测试场景"""
    # 创建一个简单的网格地图
    grid = np.zeros((15, 20), dtype=int)
    
    # 添加一些障碍物
    grid[6:9, 8:12] = 1  # 矩形障碍物
    grid[3:6, 15:18] = 1  # 另一个障碍物
    
    return grid


def run_basic_demo():
    """运行基础演示"""
    print("=" * 50)
    print("流体路径规划系统 - 基础演示")
    print("=" * 50)
    
    # 创建测试场景
    print("\n1. 创建测试场景...")
    grid_map = create_simple_scenario()
    print(f"   网格大小: {grid_map.shape}")
    
    # 创建流体路径规划器
    print("\n2. 初始化流体路径规划器...")
    planner = FluidPathPlanner(grid_map)
    
    # 准备网格（添加边界）
    print("\n3. 准备网格边界...")
    planner.prepare_grid_with_inlet_outlet(boundary_type='diagonal')
    print(f"   扩展后网格大小: {planner.grid_map.shape}")
    print(f"   入口位置: {planner.start_point}")
    print(f"   出口位置: {planner.end_point}")
    
    # 可视化初始网格
    print("\n4. 可视化初始网格...")
    try:
        grid_fig = planner.visualizer.visualize_grid_with_boundary(
            planner.grid_map, planner.start_point, planner.end_point)
        plt.title("Initial Grid with Inlet/Outlet")
        plt.show()
    except Exception as e:
        print(f"   网格可视化出错: {e}")
    
    # 运行流体模拟
    print("\n5. 运行LBM流体模拟...")
    try:
        planner.run_fluid_simulation(
            max_iter=1000,  # 减少迭代次数以加快演示
            tau=0.8,
            inlet_velocity=0.1,
            convergence_threshold=1e-6,
            collision_model='bgk'
        )
        print("   ✓ 流体模拟完成")
    except Exception as e:
        print(f"   ✗ 流体模拟出错: {e}")
        return None
    
    # 可视化流体场
    print("\n6. 可视化流体场...")
    try:
        fluid_fig = planner.visualize_fluid_fields()
        plt.show()
    except Exception as e:
        print(f"   流体场可视化出错: {e}")
    
    # 运行路径规划算法
    print("\n7. 运行路径规划算法...")
    try:
        # 经典A*
        path_astar = planner.find_path_astar_classic()
        if path_astar:
            print(f"   ✓ A*路径长度: {len(path_astar)}")
        
        # 流体增强A*
        path_fluid = planner.find_path_fluid_astar(fluid_weight=0.5)
        if path_fluid:
            print(f"   ✓ 流体A*路径长度: {len(path_fluid)}")
        
        # 速度场跟随
        path_velocity = planner.find_path_velocity_field()
        if path_velocity:
            print(f"   ✓ 速度场路径长度: {len(path_velocity)}")
            
    except Exception as e:
        print(f"   路径规划出错: {e}")
    
    # 可视化路径比较
    print("\n8. 可视化路径比较...")
    try:
        if planner.paths:
            path_fig = planner.visualize_paths()
            plt.show()
        else:
            print("   没有找到有效路径")
    except Exception as e:
        print(f"   路径可视化出错: {e}")
    
    # 显示性能总结
    print("\n9. 性能总结:")
    try:
        summary = planner.get_performance_summary()
        print(f"   网格大小: {summary['grid_size']}")
        print(f"   测试算法: {summary['algorithms_tested']}")
        print(f"   找到路径数: {summary['total_paths_found']}")
        
        if summary['performance_metrics']:
            print("   算法性能:")
            for alg, metrics in summary['performance_metrics'].items():
                if 'computation_time' in metrics:
                    print(f"     {alg}: {metrics['computation_time']:.4f}秒")
    except Exception as e:
        print(f"   性能总结出错: {e}")
    
    print("\n演示完成！")
    return planner


def test_excel_loading():
    """测试Excel数据加载"""
    print("\n测试Excel数据加载...")
    
    # 尝试加载Excel文件
    excel_path = 'label/sea_ice_281.xlsx'
    grid_data = DataProcessor.load_excel_data(excel_path)
    
    if grid_data is not None:
        print(f"✓ 成功加载Excel数据，大小: {grid_data.shape}")
        
        # 创建规划器并运行简单测试
        planner = FluidPathPlanner(grid_data)
        planner.prepare_grid_with_inlet_outlet()
        
        print(f"扩展后网格大小: {planner.grid_map.shape}")
        
        # 运行短时间的流体模拟
        planner.run_fluid_simulation(max_iter=500, tau=1.0, inlet_velocity=0.05)
        
        # 可视化结果
        planner.visualize_fluid_fields()
        plt.show()
        
        return planner
    else:
        print("✗ 无法加载Excel数据")
        return None


def main():
    """主函数"""
    print("流体路径规划系统 v2.0 - 演示程序")
    print("基于模块化设计的LBM流体模拟和路径规划")
    
    try:
        # 运行基础演示
        planner = run_basic_demo()
        
        # 询问是否测试Excel加载
        print("\n" + "="*50)
        response = input("是否测试Excel数据加载? (y/n): ").lower().strip()
        
        if response == 'y' or response == 'yes':
            excel_planner = test_excel_loading()
        
        print("\n所有演示完成！")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
