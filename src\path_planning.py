"""
路径规划算法模块
包含经典A*、<PERSON><PERSON><PERSON>、基于流体场的路径规划算法
"""

import numpy as np
import heapq
from typing import List, Tuple, Optional, Dict, Any
from scipy import ndimage
from scipy.interpolate import griddata
import time


class PathPlanningAlgorithms:
    """路径规划算法集合类"""
    
    def __init__(self, grid_map: np.ndarray):
        """
        初始化路径规划器
        
        参数:
        grid_map: 网格地图，0表示可通行，1表示障碍物
        """
        self.grid_map = grid_map
        self.nx, self.ny = grid_map.shape
        
        # 8方向移动
        self.directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                          (0, 1), (1, -1), (1, 0), (1, 1)]
        
        # 对角线移动代价
        self.sqrt2 = np.sqrt(2)
    
    def heuristic(self, a: Tuple[int, int], b: <PERSON><PERSON>[int, int], 
                 metric: str = 'euclidean') -> float:
        """
        启发式函数
        
        参数:
        a, b: 两个点的坐标
        metric: 距离度量方式 ('euclidean', 'manhattan', 'octile')
        
        返回:
        启发式距离
        """
        dx = abs(a[0] - b[0])
        dy = abs(a[1] - b[1])
        
        if metric == 'euclidean':
            return np.sqrt(dx**2 + dy**2)
        elif metric == 'manhattan':
            return dx + dy
        elif metric == 'octile':
            return max(dx, dy) + (self.sqrt2 - 1) * min(dx, dy)
        else:
            raise ValueError(f"不支持的距离度量: {metric}")
    
    def get_neighbors(self, x: int, y: int, allow_diagonal: bool = True) -> List[Tuple[int, int]]:
        """
        获取邻居节点
        
        参数:
        x, y: 当前位置
        allow_diagonal: 是否允许对角线移动
        
        返回:
        邻居节点列表
        """
        neighbors = []
        directions = self.directions if allow_diagonal else [(-1, 0), (1, 0), (0, -1), (0, 1)]
        
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            
            # 检查边界
            if 0 <= nx < self.nx and 0 <= ny < self.ny:
                # 检查是否是障碍物
                if self.grid_map[nx, ny] == 0:
                    neighbors.append((nx, ny))
        
        return neighbors
    
    def astar_classic(self, start: Tuple[int, int], goal: Tuple[int, int], 
                     allow_diagonal: bool = True, 
                     heuristic_metric: str = 'euclidean') -> Tuple[Optional[List[Tuple[int, int]]], int]:
        """
        经典A*算法
        
        参数:
        start: 起点
        goal: 终点
        allow_diagonal: 是否允许对角线移动
        heuristic_metric: 启发式度量
        
        返回:
        (路径, 迭代次数)
        """
        start_time = time.time()
        
        # 初始化
        open_set = []
        closed_set = set()
        came_from = {}
        g_score = {start: 0}
        f_score = {start: self.heuristic(start, goal, heuristic_metric)}
        
        heapq.heappush(open_set, (f_score[start], start))
        iterations = 0
        
        while open_set:
            iterations += 1
            current = heapq.heappop(open_set)[1]
            
            if current == goal:
                # 重建路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1], iterations
            
            closed_set.add(current)
            
            for neighbor in self.get_neighbors(current[0], current[1], allow_diagonal):
                if neighbor in closed_set:
                    continue
                
                # 计算移动代价
                dx = abs(neighbor[0] - current[0])
                dy = abs(neighbor[1] - current[1])
                move_cost = self.sqrt2 if dx == 1 and dy == 1 else 1.0
                
                tentative_g = g_score[current] + move_cost
                
                if neighbor not in g_score or tentative_g < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g
                    f_score[neighbor] = tentative_g + self.heuristic(neighbor, goal, heuristic_metric)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
        
        return None, iterations
    
    def dijkstra(self, start: Tuple[int, int], goal: Tuple[int, int], 
                allow_diagonal: bool = True) -> Tuple[Optional[List[Tuple[int, int]]], int]:
        """
        Dijkstra算法
        
        参数:
        start: 起点
        goal: 终点
        allow_diagonal: 是否允许对角线移动
        
        返回:
        (路径, 迭代次数)
        """
        # 初始化
        open_set = []
        closed_set = set()
        came_from = {}
        g_score = {start: 0}
        
        heapq.heappush(open_set, (0, start))
        iterations = 0
        
        while open_set:
            iterations += 1
            current = heapq.heappop(open_set)[1]
            
            if current == goal:
                # 重建路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1], iterations
            
            closed_set.add(current)
            
            for neighbor in self.get_neighbors(current[0], current[1], allow_diagonal):
                if neighbor in closed_set:
                    continue
                
                # 计算移动代价
                dx = abs(neighbor[0] - current[0])
                dy = abs(neighbor[1] - current[1])
                move_cost = self.sqrt2 if dx == 1 and dy == 1 else 1.0
                
                tentative_g = g_score[current] + move_cost
                
                if neighbor not in g_score or tentative_g < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g
                    heapq.heappush(open_set, (tentative_g, neighbor))
        
        return None, iterations
    
    def fluid_enhanced_astar(self, start: Tuple[int, int], goal: Tuple[int, int], 
                           speed_field: np.ndarray, u_field: np.ndarray, v_field: np.ndarray,
                           fluid_weight: float = 0.5, allow_diagonal: bool = True) -> Tuple[Optional[List[Tuple[int, int]]], int]:
        """
        基于流体场增强的A*算法
        
        参数:
        start: 起点
        goal: 终点
        speed_field: 速度大小场
        u_field, v_field: 速度分量场
        fluid_weight: 流体信息权重
        allow_diagonal: 是否允许对角线移动
        
        返回:
        (路径, 迭代次数)
        """
        # 预处理速度场
        smooth_speed = ndimage.gaussian_filter(speed_field, sigma=1.0)
        
        # 计算方向一致性权重
        direction_weight = self._compute_direction_weight(u_field, v_field)
        
        # 计算障碍物权重
        weight_map = self._compute_obstacle_weight()
        
        # 创建代价函数
        cost_map = weight_map * direction_weight / (smooth_speed + 1e-6)
        
        # 初始化A*
        open_set = []
        closed_set = set()
        came_from = {}
        g_score = {start: 0}
        f_score = {start: self.heuristic(start, goal)}
        
        heapq.heappush(open_set, (f_score[start], start))
        iterations = 0
        
        while open_set:
            iterations += 1
            current = heapq.heappop(open_set)[1]
            
            if current == goal:
                # 重建路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return self._smooth_path(path[::-1]), iterations
            
            closed_set.add(current)
            
            for neighbor in self.get_neighbors(current[0], current[1], allow_diagonal):
                if neighbor in closed_set:
                    continue
                
                # 基础移动代价
                dx = abs(neighbor[0] - current[0])
                dy = abs(neighbor[1] - current[1])
                base_cost = self.sqrt2 if dx == 1 and dy == 1 else 1.0
                
                # 流体场代价
                fluid_cost = cost_map[neighbor[0], neighbor[1]]
                
                # 组合代价
                move_cost = (1 - fluid_weight) * base_cost + fluid_weight * fluid_cost
                
                tentative_g = g_score[current] + move_cost
                
                if neighbor not in g_score or tentative_g < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g
                    f_score[neighbor] = tentative_g + self.heuristic(neighbor, goal)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
        
        return None, iterations
    
    def velocity_field_path(self, start: Tuple[int, int], goal: Tuple[int, int],
                          u_field: np.ndarray, v_field: np.ndarray,
                          step_size: float = 0.5, max_steps: int = 10000) -> Optional[List[Tuple[int, int]]]:
        """
        基于速度场的路径规划
        
        参数:
        start: 起点
        goal: 终点
        u_field, v_field: 速度分量场
        step_size: 步长
        max_steps: 最大步数
        
        返回:
        路径点列表
        """
        path = [start]
        current = np.array(start, dtype=float)
        goal_array = np.array(goal, dtype=float)
        
        for step in range(max_steps):
            # 检查是否到达目标
            if np.linalg.norm(current - goal_array) < 1.0:
                path.append(goal)
                break
            
            # 获取当前位置的速度
            x, y = int(current[0]), int(current[1])
            if 0 <= x < self.nx and 0 <= y < self.ny:
                if self.grid_map[x, y] == 1:  # 遇到障碍物
                    break
                
                # 插值获取精确速度
                u_interp = self._bilinear_interpolate(u_field, current[0], current[1])
                v_interp = self._bilinear_interpolate(v_field, current[0], current[1])
                
                # 添加目标导向分量
                to_goal = goal_array - current
                to_goal_norm = np.linalg.norm(to_goal)
                if to_goal_norm > 0:
                    to_goal = to_goal / to_goal_norm
                
                # 组合速度向量
                velocity = np.array([u_interp, v_interp]) + 0.3 * to_goal
                velocity_norm = np.linalg.norm(velocity)
                
                if velocity_norm > 0:
                    velocity = velocity / velocity_norm
                    current = current + step_size * velocity
                    
                    # 添加到路径
                    path.append((int(current[0]), int(current[1])))
                else:
                    break
            else:
                break
        
        return path if len(path) > 1 else None
    
    def _compute_direction_weight(self, u_field: np.ndarray, v_field: np.ndarray) -> np.ndarray:
        """计算方向一致性权重"""
        # 计算速度场的散度和旋度
        du_dx = np.gradient(u_field, axis=1)
        dv_dy = np.gradient(v_field, axis=0)
        divergence = du_dx + dv_dy
        
        du_dy = np.gradient(u_field, axis=0)
        dv_dx = np.gradient(v_field, axis=1)
        curl = dv_dx - du_dy
        
        # 方向一致性权重（低散度和低旋度的区域权重更高）
        direction_weight = 1.0 / (1.0 + np.abs(divergence) + np.abs(curl))
        
        return direction_weight
    
    def _compute_obstacle_weight(self) -> np.ndarray:
        """计算障碍物权重"""
        # 计算到最近障碍物的距离
        obstacle_mask = (self.grid_map == 1)
        distance_to_obstacle = ndimage.distance_transform_edt(~obstacle_mask)
        
        # 距离障碍物越远权重越低（代价越小）
        weight_map = 1.0 / (1.0 + distance_to_obstacle)
        
        return weight_map
    
    def _smooth_path(self, path: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """路径平滑处理"""
        if len(path) <= 2:
            return path
        
        smoothed = [path[0]]
        i = 0
        
        while i < len(path) - 1:
            # 尝试直线连接到更远的点
            for j in range(len(path) - 1, i, -1):
                if self._line_of_sight(path[i], path[j]):
                    smoothed.append(path[j])
                    i = j
                    break
            else:
                i += 1
                if i < len(path):
                    smoothed.append(path[i])
        
        return smoothed
    
    def _line_of_sight(self, start: Tuple[int, int], end: Tuple[int, int]) -> bool:
        """检查两点间是否有直线视线"""
        x0, y0 = start
        x1, y1 = end
        
        # Bresenham直线算法
        dx = abs(x1 - x0)
        dy = abs(y1 - y0)
        sx = 1 if x0 < x1 else -1
        sy = 1 if y0 < y1 else -1
        err = dx - dy
        
        x, y = x0, y0
        
        while True:
            if self.grid_map[x, y] == 1:  # 遇到障碍物
                return False
            
            if x == x1 and y == y1:
                break
            
            e2 = 2 * err
            if e2 > -dy:
                err -= dy
                x += sx
            if e2 < dx:
                err += dx
                y += sy
        
        return True
    
    def _bilinear_interpolate(self, field: np.ndarray, x: float, y: float) -> float:
        """双线性插值"""
        x0, y0 = int(x), int(y)
        x1, y1 = min(x0 + 1, self.nx - 1), min(y0 + 1, self.ny - 1)
        
        if x0 == x1 and y0 == y1:
            return field[x0, y0]
        
        # 插值权重
        wx = x - x0
        wy = y - y0
        
        # 双线性插值
        result = (field[x0, y0] * (1 - wx) * (1 - wy) +
                 field[x1, y0] * wx * (1 - wy) +
                 field[x0, y1] * (1 - wx) * wy +
                 field[x1, y1] * wx * wy)
        
        return result
