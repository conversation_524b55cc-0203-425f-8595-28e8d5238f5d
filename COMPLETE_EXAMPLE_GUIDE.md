# 🚀 完整示例使用指南

## 📋 文件说明

`complete_example.py` 是一个包含所有功能和超参数调整的完整示例入口文件，专为您后续使用此算法而设计。

## 🎯 主要特性

### ✨ 完整功能覆盖
- **LBM流体模拟**: 支持BGK和MRT碰撞模型
- **多种路径规划算法**: A*、Dijkstra、流体增强A*、速度场跟随
- **全面可视化**: 2D/3D图表、路径比较、性能分析
- **数据处理**: Excel导入导出、结果保存
- **性能分析**: 详细的算法性能指标

### 🔧 灵活的参数配置
- **场景设置**: 5种预定义场景类型
- **LBM参数**: 完整的物理参数控制
- **算法选择**: 可选择运行的算法组合
- **可视化控制**: 灵活的显示和保存选项
- **高级参数**: 雷诺数控制、自适应参数等

## 🚀 快速开始

### 1. 基础运行
```bash
# 直接运行（使用默认参数）
python complete_example.py

# 快速测试模式
python complete_example.py test
```

### 2. 自定义参数运行
在 `main()` 函数中修改参数，然后运行：

```python
# 修改场景类型
config['scenario_type'] = 'maze'  # simple, complex, channel, maze, custom

# 调整LBM参数
config['lbm_params']['tau'] = 1.0              # 松弛时间
config['lbm_params']['inlet_velocity'] = 0.15  # 入口速度
config['lbm_params']['max_iter'] = 5000        # 最大迭代次数

# 选择算法
config['algorithms'] = ['astar_classic', 'fluid_astar']

# 设置输出
config['output_dir'] = 'my_results'
config['save_results'] = True
```

## 📊 参数详细说明

### 🏗️ 基本设置
```python
'scenario_type': 'complex'        # 场景类型
'excel_file': None                # Excel文件路径
'start_point': None               # 起点坐标 (自动确定)
'end_point': None                 # 终点坐标 (自动确定)
'boundary_type': 'diagonal'       # 边界类型
```

**场景类型说明**:
- `'simple'`: 简单矩形障碍物
- `'complex'`: 复杂多障碍物场景
- `'channel'`: 收缩通道流动
- `'maze'`: 迷宫结构
- `'custom'`: 自定义设计

### 🌊 LBM仿真参数
```python
'lbm_params': {
    'max_iter': 3000,              # 最大迭代次数 (1000-10000)
    'tau': 0.8,                    # 松弛时间 (0.5-2.0)
    'inlet_velocity': 0.1,         # 入口速度 (0.01-0.2)
    'convergence_threshold': 1e-6, # 收敛阈值
    'collision_model': 'bgk',      # 碰撞模型: bgk, mrt
    'boundary_condition': 'zou_he' # 边界条件: zou_he, equilibrium
}
```

**参数影响**:
- `tau`: 控制流体粘性，值越大粘性越高
- `inlet_velocity`: 影响流动强度和雷诺数
- `max_iter`: 影响收敛精度和计算时间
- `collision_model`: BGK简单快速，MRT更稳定

### 🗺️ 路径规划参数
```python
'algorithms': [                    # 算法选择
    'astar_classic',               # 经典A*
    'dijkstra',                    # Dijkstra
    'fluid_astar',                 # 流体增强A*
    'velocity_field'               # 速度场跟随
],

'path_params': {
    'allow_diagonal': True,        # 对角线移动
    'fluid_weight': 0.7,          # 流体权重 (0.0-1.0)
    'step_size': 0.5,             # 跟随步长
    'max_steps': 10000            # 最大步数
}
```

**算法特点**:
- `astar_classic`: 快速、可靠的基准算法
- `dijkstra`: 保证最优路径
- `fluid_astar`: 考虑流体动力学的智能路径
- `velocity_field`: 自然流动路径

### 📊 可视化设置
```python
'show_initial_grid': True,        # 显示初始网格
'show_fluid_fields': True,        # 显示流体场
'show_3d_visualization': True,     # 显示3D可视化
'show_path_comparison': True,      # 显示路径比较
'show_performance_metrics': True,  # 显示性能指标
'show_plots': True,               # 是否显示图表

'3d_fields': ['speed', 'pressure'], # 3D可视化字段
```

### 💾 输出设置
```python
'save_results': True,             # 保存结果
'output_dir': 'simulation_output', # 输出目录
```

## 🎮 使用场景示例

### 场景1: 快速验证算法
```python
def quick_validation():
    config = get_default_config()
    config['scenario_type'] = 'simple'
    config['lbm_params']['max_iter'] = 1000
    config['algorithms'] = ['astar_classic', 'fluid_astar']
    config['show_plots'] = False
    config['save_results'] = True
    # ... 运行仿真
```

### 场景2: 详细性能分析
```python
def performance_analysis():
    config = get_default_config()
    config['scenario_type'] = 'complex'
    config['lbm_params']['max_iter'] = 5000
    config['algorithms'] = ['astar_classic', 'dijkstra', 'fluid_astar', 'velocity_field']
    config['show_performance_metrics'] = True
    config['save_results'] = True
    # ... 运行仿真
```

### 场景3: 高雷诺数流动
```python
def high_reynolds_flow():
    config = get_default_config()
    config['lbm_params']['tau'] = 0.6          # 低粘性
    config['lbm_params']['inlet_velocity'] = 0.2  # 高速度
    config['lbm_params']['max_iter'] = 8000    # 更多迭代
    config['show_3d_visualization'] = True
    # ... 运行仿真
```

### 场景4: Excel数据处理
```python
def excel_data_processing():
    config = get_default_config()
    config['excel_file'] = 'your_data.xlsx'
    config['lbm_params']['tau'] = 1.0
    config['lbm_params']['inlet_velocity'] = 0.05
    config['save_results'] = True
    # ... 运行仿真
```

## 📈 输出结果说明

### 文件结构
```
output_dir/
├── initial_grid.png              # 初始网格图
├── fluid_fields.png              # 流体场可视化
├── 3d_speed.png                  # 3D速度场
├── 3d_pressure.png               # 3D压力场
├── path_comparison.png           # 路径比较图
├── performance_metrics.png       # 性能指标图
├── velocity_field.xlsx           # 速度场数据
├── pressure_field.xlsx           # 压力场数据
├── u_velocity.xlsx               # X方向速度
├── v_velocity.xlsx               # Y方向速度
├── path_*.xlsx                   # 各算法路径数据
├── performance_metrics.xlsx      # 性能指标数据
└── simulation_config.json        # 仿真配置文件
```

### 性能指标
- **path_length**: 路径点数
- **euclidean_length**: 欧几里得距离
- **curvature**: 路径曲率（平滑度）
- **avg_speed**: 路径平均速度
- **efficiency**: 路径效率
- **computation_time**: 计算时间

## 🔧 高级使用技巧

### 1. 参数优化
```python
# 根据网格大小调整参数
grid_size = grid_map.shape[0] * grid_map.shape[1]
if grid_size > 1000:
    config['lbm_params']['max_iter'] = 5000
    config['lbm_params']['tau'] = 0.9
```

### 2. 批量测试
```python
# 测试不同tau值
tau_values = [0.6, 0.8, 1.0, 1.2]
for tau in tau_values:
    config['lbm_params']['tau'] = tau
    config['output_dir'] = f'results_tau_{tau}'
    # ... 运行仿真
```

### 3. 自定义场景
```python
def create_my_scenario():
    grid = np.zeros((30, 40), dtype=int)
    # 添加您的障碍物设计
    grid[10:15, 15:25] = 1  # 矩形障碍物
    # 添加圆形障碍物
    center_x, center_y = 20, 30
    for i in range(grid.shape[0]):
        for j in range(grid.shape[1]):
            if (i - center_x)**2 + (j - center_y)**2 <= 25:
                grid[i, j] = 1
    return grid
```

## 🚨 注意事项

### 数值稳定性
- `tau` 值过小可能导致数值不稳定
- `inlet_velocity` 过大可能导致发散
- 复杂场景需要更多迭代次数

### 性能考虑
- 大网格需要更多计算时间
- 3D可视化消耗较多内存
- 保存大量数据需要足够磁盘空间

### 常见问题
1. **模拟不收敛**: 降低`inlet_velocity`或增加`tau`
2. **路径规划失败**: 检查起点终点是否在流体区域
3. **内存不足**: 减小网格大小或关闭3D可视化

## 🎯 最佳实践

1. **首次使用**: 先运行快速测试验证环境
2. **参数调试**: 从简单场景开始，逐步增加复杂度
3. **结果分析**: 保存配置文件便于重现实验
4. **性能优化**: 根据需求选择合适的算法组合

---

**这个完整示例文件为您提供了使用流体路径规划算法的所有必要工具和参数控制，便于您根据具体需求进行定制和使用！** 🎉
