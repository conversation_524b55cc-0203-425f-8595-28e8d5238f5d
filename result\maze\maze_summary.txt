场景: maze
描述: 迷宫场景
网格大小: (31, 31)
入口位置: (0, 0)
出口位置: (27, 27)
测试算法: ['A* Classic', 'Dijkstra', 'Fluid A*', 'Velocity Field']
找到路径数: 4
模拟时间: 0.75秒

算法性能指标:

A* Classic:
  path_length: 39
  euclidean_length: 44.627417
  curvature: 0.486486
  avg_speed: 0.013449
  min_speed: 0.001498
  max_speed: 0.113137
  efficiency: 0.855612
  computation_time: 0.001998
  iterations: 353

Dijkstra:
  path_length: 39
  euclidean_length: 44.627417
  curvature: 0.189189
  avg_speed: 0.014190
  min_speed: 0.000883
  max_speed: 0.113137
  efficiency: 0.855612
  computation_time: 0.002025
  iterations: 582

Fluid A*:
  path_length: 4
  euclidean_length: 42.845005
  curvature: 12.584327
  avg_speed: 0.040078
  min_speed: 0.005355
  max_speed: 0.113137
  efficiency: 0.891207
  computation_time: 0.003488
  iterations: 394
  fluid_weight: 0.700000

Velocity Field:
  path_length: 24
  euclidean_length: 11.313708
  curvature: 0.964237
  avg_speed: 0.039107
  min_speed: 0.000000
  max_speed: 0.113137
  efficiency: 1.000000
  computation_time: 0.000000
  step_size: 0.500000
