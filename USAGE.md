# 快速使用指南

## 安装依赖
```bash
pip install -r requirements.txt
```

## 运行演示
```bash
# 基础演示
python examples/demo.py

# 批量测试
python examples/run_and_save_results.py

# 主入口
python main.py
```

## 基础使用
```python
import sys
sys.path.append('src')

from fluid_path_planner import FluidPathPlanner
import numpy as np

# 创建网格地图
grid = np.zeros((20, 30), dtype=int)
grid[8:12, 10:20] = 1  # 添加障碍物

# 创建规划器
planner = FluidPathPlanner(grid)

# 运行完整流程
planner.prepare_grid_with_inlet_outlet()
planner.run_fluid_simulation()
paths, metrics = planner.compare_all_paths()

# 可视化
planner.visualize_fluid_fields()
planner.visualize_paths()
```

## 查看结果
- 测试结果保存在 `result/` 文件夹
- 包含图像、数据文件和性能报告
- 支持Excel格式的数据导出
