{"scenario_type": "complex", "excel_file": null, "start_point": null, "end_point": null, "boundary_type": "diagonal", "lbm_params": {"max_iter": 2000, "tau": 0.8, "inlet_velocity": 0.1, "convergence_threshold": 1e-06, "collision_model": "bgk", "boundary_condition": "zou_he"}, "algorithms": ["astar_classic", "<PERSON><PERSON><PERSON>", "fluid_astar", "velocity_field"], "path_params": {"allow_diagonal": true, "fluid_weight": 0.7, "step_size": 0.5, "max_steps": 10000}, "show_initial_grid": true, "show_fluid_fields": true, "show_3d_visualization": true, "show_path_comparison": true, "show_performance_metrics": true, "show_plots": true, "3d_fields": ["speed", "pressure"], "save_results": true, "output_dir": "my_simulation", "advanced_params": {"reynolds_number_target": null, "grid_refinement": 1, "adaptive_tau": false, "turbulence_model": null}}