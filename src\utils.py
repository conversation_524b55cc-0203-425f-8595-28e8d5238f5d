"""
工具函数模块
包含数据处理、网格操作、性能分析等实用函数
"""

import numpy as np
import pandas as pd
import time
from typing import Tuple, List, Optional, Dict, Any
from scipy import ndimage
import os


class GridProcessor:
    """网格处理工具类"""
    
    @staticmethod
    def add_boundary(grid_map: np.ndarray, boundary_type: str = 'diagonal') -> np.ndarray:
        """
        在地图四周添加边界
        
        参数:
        grid_map: 原始网格地图
        boundary_type: 边界类型 ('diagonal', 'rectangular', 'custom')
        
        返回:
        添加边界后的网格地图
        """
        h, w = grid_map.shape
        extension_size = max(int(min(h, w) * 0.1), 1)
        
        # 创建新的扩展地图
        new_h = h + 2 + 2 * extension_size
        new_w = w + 2 + 2 * extension_size
        new_map = np.ones((new_h, new_w))
        
        # 复制原始地图到中心
        new_map[extension_size+1:-extension_size-1, extension_size+1:-extension_size-1] = grid_map
        
        if boundary_type == 'diagonal':
            # 对角线形式的入口和出口
            GridProcessor._create_diagonal_boundaries(new_map, extension_size)
        elif boundary_type == 'rectangular':
            # 矩形形式的入口和出口
            GridProcessor._create_rectangular_boundaries(new_map, extension_size)
        elif boundary_type == 'custom':
            # 自定义边界
            GridProcessor._create_custom_boundaries(new_map, extension_size)
        
        return new_map
    
    @staticmethod
    def _create_diagonal_boundaries(new_map: np.ndarray, extension_size: int) -> None:
        """创建对角线边界"""
        new_h, new_w = new_map.shape
        
        # 左上角入口
        for i in range(2 * extension_size + 1):
            for j in range(2 * extension_size + 1):
                if i == j or abs(i - j) <= extension_size // 2:
                    new_map[i, j] = 0
        
        # 右下角出口
        for i in range(new_h - 2 * extension_size - 1, new_h):
            for j in range(new_w - 2 * extension_size - 1, new_w):
                rel_i = i - (new_h - 2 * extension_size - 1)
                rel_j = j - (new_w - 2 * extension_size - 1)
                if rel_i == rel_j or abs(rel_i - rel_j) <= extension_size // 2:
                    new_map[i, j] = 0
    
    @staticmethod
    def _create_rectangular_boundaries(new_map: np.ndarray, extension_size: int) -> None:
        """创建矩形边界"""
        new_h, new_w = new_map.shape
        
        # 左上角入口（矩形）
        new_map[0:extension_size+1, 0:extension_size+1] = 0
        
        # 右下角出口（矩形）
        new_map[new_h-extension_size-1:new_h, new_w-extension_size-1:new_w] = 0
    
    @staticmethod
    def _create_custom_boundaries(new_map: np.ndarray, extension_size: int) -> None:
        """创建自定义边界"""
        new_h, new_w = new_map.shape
        
        # 圆形入口
        center_inlet = (extension_size // 2, extension_size // 2)
        radius = extension_size // 3
        
        for i in range(extension_size + 1):
            for j in range(extension_size + 1):
                if (i - center_inlet[0])**2 + (j - center_inlet[1])**2 <= radius**2:
                    new_map[i, j] = 0
        
        # 圆形出口
        center_outlet = (new_h - extension_size // 2 - 1, new_w - extension_size // 2 - 1)
        
        for i in range(new_h - extension_size - 1, new_h):
            for j in range(new_w - extension_size - 1, new_w):
                if (i - center_outlet[0])**2 + (j - center_outlet[1])**2 <= radius**2:
                    new_map[i, j] = 0
    
    @staticmethod
    def find_inlet_outlet_positions(grid_map: np.ndarray) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """
        自动找到入口和出口位置
        
        参数:
        grid_map: 网格地图
        
        返回:
        (入口位置, 出口位置)
        """
        h, w = grid_map.shape
        extension_size = max(int(min(h, w) * 0.1), 1)
        
        # 寻找左上角入口中心
        inlet_center = None
        for i in range(extension_size + 1):
            for j in range(extension_size + 1):
                if grid_map[i, j] == 0:
                    if i == j:  # 对角线上的点
                        inlet_center = (i, j)
                        break
            if inlet_center:
                break
        
        if not inlet_center:
            # 如果没找到对角线点，使用第一个流体点
            for i in range(extension_size + 1):
                for j in range(extension_size + 1):
                    if grid_map[i, j] == 0:
                        inlet_center = (i, j)
                        break
                if inlet_center:
                    break
        
        # 寻找右下角出口中心
        outlet_center = None
        for i in range(h - extension_size - 1, h):
            for j in range(w - extension_size - 1, w):
                if grid_map[i, j] == 0:
                    rel_i = i - (h - extension_size - 1)
                    rel_j = j - (w - extension_size - 1)
                    if rel_i == rel_j:  # 对角线上的点
                        outlet_center = (i, j)
                        break
            if outlet_center:
                break
        
        if not outlet_center:
            # 如果没找到对角线点，使用最后一个流体点
            for i in range(h - 1, h - extension_size - 2, -1):
                for j in range(w - 1, w - extension_size - 2, -1):
                    if grid_map[i, j] == 0:
                        outlet_center = (i, j)
                        break
                if outlet_center:
                    break
        
        return inlet_center or (1, 1), outlet_center or (h-2, w-2)


class DataProcessor:
    """数据处理工具类"""
    
    @staticmethod
    def load_excel_data(file_path: str) -> Optional[np.ndarray]:
        """
        从Excel文件加载数据
        
        参数:
        file_path: Excel文件路径
        
        返回:
        二进制网格地图或None
        """
        try:
            data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
            numpy_array = data.to_numpy()
            
            # 转换为二进制地图：0表示流体，1表示障碍物
            binary_map = np.where(numpy_array == 0, 255, np.where(numpy_array == 255, 0, numpy_array))
            binary_map = binary_map / 255
            binary_map = binary_map.astype(int)
            
            return binary_map
        except Exception as e:
            print(f"加载Excel数据时出错: {e}")
            return None
    
    @staticmethod
    def save_results_to_excel(data: np.ndarray, file_path: str, sheet_name: str = 'Sheet1') -> None:
        """
        保存结果到Excel文件
        
        参数:
        data: 要保存的数据
        file_path: 保存路径
        sheet_name: 工作表名称
        """
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            df = pd.DataFrame(data)
            df.to_excel(file_path, sheet_name=sheet_name, index=False, header=False)
            print(f"结果已保存至: {file_path}")
        except Exception as e:
            print(f"保存Excel文件时出错: {e}")
    
    @staticmethod
    def create_sample_grid(size: Tuple[int, int], obstacle_density: float = 0.3) -> np.ndarray:
        """
        创建示例网格地图
        
        参数:
        size: 网格大小 (height, width)
        obstacle_density: 障碍物密度
        
        返回:
        网格地图
        """
        h, w = size
        grid = np.zeros((h, w), dtype=int)
        
        # 随机添加障碍物
        np.random.seed(42)
        obstacle_mask = np.random.random((h, w)) < obstacle_density
        grid[obstacle_mask] = 1
        
        # 确保边界附近有通道
        grid[0, :] = 0
        grid[-1, :] = 0
        grid[:, 0] = 0
        grid[:, -1] = 0
        
        # 添加一些结构化障碍物
        # 垂直墙
        if w > 10:
            wall_x = w // 3
            wall_start = h // 4
            wall_end = 3 * h // 4
            grid[wall_start:wall_end, wall_x] = 1
            
            # 在墙上开个口
            gap_start = wall_start + (wall_end - wall_start) // 3
            gap_end = gap_start + (wall_end - wall_start) // 6
            grid[gap_start:gap_end, wall_x] = 0
        
        return grid


class PerformanceAnalyzer:
    """性能分析工具类"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.timers = {}
        self.metrics = {}
    
    def start_timer(self, name: str) -> None:
        """开始计时"""
        self.timers[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        if name in self.timers:
            elapsed = time.time() - self.timers[name]
            self.metrics[f"{name}_time"] = elapsed
            return elapsed
        return 0.0
    
    def calculate_path_metrics(self, path: List[Tuple[int, int]], 
                             speed_field: np.ndarray) -> Dict[str, float]:
        """
        计算路径性能指标
        
        参数:
        path: 路径点列表
        speed_field: 速度场
        
        返回:
        性能指标字典
        """
        if not path or len(path) < 2:
            return {}
        
        path_array = np.array(path)
        
        # 路径长度
        path_length = len(path)
        
        # 欧几里得距离
        euclidean_length = np.sum(np.sqrt(np.sum(np.diff(path_array, axis=0)**2, axis=1)))
        
        # 路径平滑度（曲率）
        if len(path) > 2:
            # 计算路径的二阶导数作为曲率的近似
            second_diff = np.diff(path_array, n=2, axis=0)
            curvature = np.mean(np.sqrt(np.sum(second_diff**2, axis=1)))
        else:
            curvature = 0.0
        
        # 路径上的平均速度
        path_speeds = []
        for x, y in path:
            if 0 <= x < speed_field.shape[0] and 0 <= y < speed_field.shape[1]:
                path_speeds.append(speed_field[x, y])
        
        avg_speed = np.mean(path_speeds) if path_speeds else 0.0
        min_speed = np.min(path_speeds) if path_speeds else 0.0
        max_speed = np.max(path_speeds) if path_speeds else 0.0
        
        # 路径效率（直线距离/实际路径长度）
        if len(path) >= 2:
            straight_line_distance = np.sqrt((path[-1][0] - path[0][0])**2 + 
                                           (path[-1][1] - path[0][1])**2)
            efficiency = straight_line_distance / euclidean_length if euclidean_length > 0 else 0
        else:
            efficiency = 0.0
        
        return {
            'path_length': path_length,
            'euclidean_length': euclidean_length,
            'curvature': curvature,
            'avg_speed': avg_speed,
            'min_speed': min_speed,
            'max_speed': max_speed,
            'efficiency': efficiency
        }
    
    def calculate_reynolds_number(self, u_max: float, characteristic_length: float, 
                                tau: float) -> float:
        """
        计算雷诺数
        
        参数:
        u_max: 最大速度
        characteristic_length: 特征长度
        tau: 松弛时间
        
        返回:
        雷诺数
        """
        nu = (tau - 0.5) / 3.0  # LBM中的运动粘度
        return u_max * characteristic_length / nu
    
    def analyze_convergence(self, convergence_history: List[float]) -> Dict[str, float]:
        """
        分析收敛性能
        
        参数:
        convergence_history: 收敛历史
        
        返回:
        收敛分析结果
        """
        if not convergence_history:
            return {}
        
        # 收敛迭代次数
        convergence_iterations = len(convergence_history)
        
        # 最终误差
        final_error = convergence_history[-1]
        
        # 收敛速度（指数拟合）
        if len(convergence_history) > 10:
            # 取对数进行线性拟合
            log_errors = np.log(np.array(convergence_history) + 1e-15)
            iterations = np.arange(len(convergence_history))
            
            # 线性拟合 log(error) = a * iteration + b
            coeffs = np.polyfit(iterations, log_errors, 1)
            convergence_rate = -coeffs[0]  # 收敛速度
        else:
            convergence_rate = 0.0
        
        # 收敛稳定性（后半段的标准差）
        if len(convergence_history) > 20:
            second_half = convergence_history[len(convergence_history)//2:]
            stability = np.std(second_half)
        else:
            stability = np.std(convergence_history)
        
        return {
            'convergence_iterations': convergence_iterations,
            'final_error': final_error,
            'convergence_rate': convergence_rate,
            'stability': stability
        }
    
    def get_all_metrics(self) -> Dict[str, float]:
        """获取所有性能指标"""
        return self.metrics.copy()
    
    def reset_metrics(self) -> None:
        """重置所有指标"""
        self.timers.clear()
        self.metrics.clear()


class FluidAnalyzer:
    """流体场分析工具类"""
    
    @staticmethod
    def calculate_vorticity(u: np.ndarray, v: np.ndarray) -> np.ndarray:
        """
        计算涡量场
        
        参数:
        u, v: 速度分量
        
        返回:
        涡量场
        """
        vorticity = np.zeros_like(u)
        # 使用中心差分计算涡量
        vorticity[1:-1, 1:-1] = (v[2:, 1:-1] - v[:-2, 1:-1]) - (u[1:-1, 2:] - u[1:-1, :-2])
        return vorticity
    
    @staticmethod
    def calculate_divergence(u: np.ndarray, v: np.ndarray) -> np.ndarray:
        """
        计算散度场
        
        参数:
        u, v: 速度分量
        
        返回:
        散度场
        """
        du_dx = np.gradient(u, axis=1)
        dv_dy = np.gradient(v, axis=0)
        return du_dx + dv_dy
    
    @staticmethod
    def calculate_stream_function(u: np.ndarray, v: np.ndarray) -> np.ndarray:
        """
        计算流函数
        
        参数:
        u, v: 速度分量
        
        返回:
        流函数
        """
        # 简化的流函数计算（假设无旋流动）
        psi = np.zeros_like(u)
        
        # 积分v得到psi
        for i in range(1, psi.shape[0]):
            psi[i, :] = psi[i-1, :] + v[i, :]
        
        return psi
    
    @staticmethod
    def identify_recirculation_zones(u: np.ndarray, v: np.ndarray, 
                                   threshold: float = 1e-6) -> np.ndarray:
        """
        识别回流区
        
        参数:
        u, v: 速度分量
        threshold: 速度阈值
        
        返回:
        回流区掩码
        """
        speed = np.sqrt(u**2 + v**2)
        vorticity = FluidAnalyzer.calculate_vorticity(u, v)
        
        # 低速且高涡量的区域可能是回流区
        recirculation_mask = (speed < threshold) & (np.abs(vorticity) > np.percentile(np.abs(vorticity), 75))
        
        return recirculation_mask
