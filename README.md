# 流体路径规划系统 v2.0

基于格子玻尔兹曼方法(LBM)的智能路径规划系统，集成了先进的流体模拟和多种路径规划算法。

## 🚀 主要特性

- **LBM流体模拟**: 支持BGK和MRT碰撞模型，改进的边界条件
- **多种路径规划算法**: A*、Dijkstra、流体增强A*、速度场跟随
- **全面可视化**: 2D/3D流体场、路径比较、性能分析
- **模块化设计**: 清晰的代码结构，便于使用和扩展

## 📁 文件结构

```
├── complete_example.py      # 🎯 主要使用文件（包含所有功能）
├── requirements.txt         # 📦 依赖包列表
├── README.md               # 📖 说明文档
├── src/                    # 💻 核心源代码
│   ├── lbm_core.py        # 🌊 LBM核心算法
│   ├── path_planning.py   # 🗺️ 路径规划算法
│   ├── visualization.py   # 📊 可视化功能
│   ├── utils.py           # 🔧 工具函数
│   └── fluid_path_planner.py # 🎯 主集成类
└── result/                 # 📈 测试结果
    ├── simple_obstacle/    # 简单障碍物场景结果
    ├── maze/              # 迷宫场景结果
    └── overall_report.txt  # 总体测试报告
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行完整示例
```bash
# 直接运行（使用默认参数）
python complete_example.py

# 快速测试模式
python complete_example.py test
```

### 3. 自定义参数使用
编辑 `complete_example.py` 文件中的参数：

```python
def main():
    config = get_default_config()

    # 修改场景类型
    config['scenario_type'] = 'maze'  # simple, complex, channel, maze, custom

    # 调整LBM参数
    config['lbm_params']['tau'] = 1.0              # 松弛时间
    config['lbm_params']['inlet_velocity'] = 0.15  # 入口速度
    config['lbm_params']['max_iter'] = 5000        # 最大迭代次数

    # 选择算法
    config['algorithms'] = ['astar_classic', 'fluid_astar']

    # 设置输出
    config['output_dir'] = 'my_results'
    config['save_results'] = True
```

### 4. 使用Excel数据
```python
# 在main()函数中设置
config['excel_file'] = 'your_data.xlsx'
```

## 📊 主要功能

### 算法支持
- **经典A***: 快速、可靠的基准算法
- **Dijkstra**: 保证最优路径
- **流体增强A***: 考虑流体动力学的智能路径
- **速度场跟随**: 自然流动路径

### 可视化功能
- 2D流体场（速度、压力、涡量）
- 3D表面可视化
- 路径比较和性能分析
- 高质量图表输出

### 结果输出
- Excel格式的数据文件
- PNG格式的可视化图表
- 详细的性能分析报告
- 完整的配置文件保存

## 📚 依赖包

```
numpy >= 1.19.0
matplotlib >= 3.3.0
scipy >= 1.5.0
pandas >= 1.1.0
openpyxl >= 3.0.0
```

## 📝 使用说明

`complete_example.py` 文件包含了所有功能和参数配置选项，是您使用此算法的主要入口文件。通过修改其中的配置参数，可以实现各种不同的仿真需求。

---

**流体路径规划系统 v2.0** - 智能路径规划，高效流体模拟！
