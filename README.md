# 流体路径规划系统 v2.0

基于格子玻尔兹曼方法(LBM)的智能路径规划系统，采用模块化设计，集成了先进的流体模拟和多种路径规划算法。

## 🚀 主要特性

### 1. 模块化设计
- **清晰的代码结构**：每个功能模块独立，便于维护和扩展
- **面向对象设计**：提高代码复用性和可读性
- **统一的接口**：简化使用和集成

### 2. 改进的LBM实现
- **多松弛时间(MRT)模型**：提高数值稳定性
- **自适应时间步长**：优化收敛性能
- **改进的边界条件**：支持Zou-He和平衡态边界条件
- **内存优化**：向量化计算，提高效率

### 3. 多种路径规划算法
- **经典A*算法**：基准算法
- **Dijkstra算法**：最短路径保证
- **流体增强A*算法**：利用流体场信息优化路径
- **速度场跟随算法**：基于流体速度场的路径规划

### 4. 全面的可视化功能
- **2D流体场可视化**：速度、压力、涡量场
- **3D表面可视化**：立体展示流体特性
- **路径比较可视化**：多算法性能对比
- **交互式图表**：支持动画和实时监控
- **性能指标图表**：详细的算法性能分析

### 5. 性能分析和指标
- **收敛性分析**：监控模拟收敛过程
- **路径质量评估**：长度、平滑度、效率等指标
- **计算性能统计**：时间复杂度和空间复杂度分析
- **雷诺数计算**：流体力学特征参数

## 📁 文件结构

```
├── lbm_core.py              # LBM核心模块
├── path_planning.py         # 路径规划算法模块
├── visualization.py         # 可视化模块
├── utils.py                 # 工具函数模块
├── fluid_path_planner.py    # 主集成类
├── FluidPathPlanning.py     # 主程序文件（兼容旧版本）
├── demo.py                  # 演示脚本
└── README.md               # 说明文档
```

## 🛠️ 模块说明

### LBM核心模块 (`lbm_core.py`)
- `LBMCore`类：实现D2Q9模型的LBM核心算法
- 支持BGK和MRT碰撞模型
- 优化的边界条件处理
- 向量化计算实现

### 路径规划模块 (`path_planning.py`)
- `PathPlanningAlgorithms`类：集成多种路径规划算法
- 支持对角线移动和多种启发式函数
- 流体场增强的路径规划
- 路径平滑和优化

### 可视化模块 (`visualization.py`)
- `FluidVisualization`类：提供全面的可视化功能
- 2D/3D可视化支持
- 自定义颜色映射
- 动画和交互式图表

### 工具模块 (`utils.py`)
- `GridProcessor`：网格处理工具
- `DataProcessor`：数据输入输出处理
- `PerformanceAnalyzer`：性能分析工具
- `FluidAnalyzer`：流体场分析工具

### 主集成类 (`fluid_path_planner.py`)
- `FluidPathPlanner`：主要的集成类
- 统一的接口和工作流程
- 自动化的参数管理
- 结果保存和导出

## 🚀 快速开始

### 1. 基础使用

```python
from fluid_path_planner import FluidPathPlanner
import numpy as np

# 创建网格地图（0=流体，1=障碍物）
grid_map = np.zeros((20, 30), dtype=int)
grid_map[8:12, 10:20] = 1  # 添加障碍物

# 创建规划器
planner = FluidPathPlanner(grid_map)

# 准备网格
planner.prepare_grid_with_inlet_outlet()

# 运行流体模拟
planner.run_fluid_simulation(max_iter=3000, tau=0.8, inlet_velocity=0.1)

# 比较所有路径规划算法
paths, metrics = planner.compare_all_paths()

# 可视化结果
planner.visualize_fluid_fields()
planner.visualize_paths()
```

### 2. 运行演示

```bash
python demo.py
```

### 3. 使用Excel数据

```python
from utils import DataProcessor

# 加载Excel数据
grid_data = DataProcessor.load_excel_data('your_file.xlsx')

# 创建规划器
planner = FluidPathPlanner(grid_data)
# ... 其他步骤相同
```

## 📊 算法比较

系统支持以下算法的性能比较：

| 算法 | 特点 | 适用场景 |
|------|------|----------|
| 经典A* | 快速、可靠 | 静态环境 |
| Dijkstra | 最优路径保证 | 需要全局最优解 |
| 流体增强A* | 考虑流体动力学 | 流体环境导航 |
| 速度场跟随 | 自然流动路径 | 能耗优化 |

## 🎯 性能指标

系统自动计算以下性能指标：

- **路径长度**：总路径点数和欧几里得距离
- **路径平滑度**：基于曲率的平滑度评估
- **计算效率**：算法执行时间和迭代次数
- **路径质量**：平均速度、最小速度、路径效率
- **收敛性能**：模拟收敛速度和稳定性

## 🔧 高级配置

### LBM参数调整

```python
planner.run_fluid_simulation(
    max_iter=5000,           # 最大迭代次数
    tau=0.8,                 # 松弛时间（影响粘性）
    inlet_velocity=0.1,      # 入口速度
    collision_model='mrt',   # 碰撞模型：'bgk'或'mrt'
    boundary_condition='zou_he',  # 边界条件类型
    convergence_threshold=1e-6    # 收敛阈值
)
```

### 路径规划参数

```python
# 流体增强A*
path = planner.find_path_fluid_astar(
    fluid_weight=0.7,        # 流体信息权重
    allow_diagonal=True      # 允许对角线移动
)

# 速度场跟随
path = planner.find_path_velocity_field(
    step_size=0.5,          # 步长
    max_steps=10000         # 最大步数
)
```

## 📈 可视化选项

### 流体场可视化
- 速度大小场
- 压力场（密度）
- 涡量场
- 流线图
- 速度矢量场
- 收敛历史

### 3D可视化
- 速度场3D表面
- 压力场3D表面
- 涡量场3D表面

### 路径比较
- 多算法路径对比
- 性能指标图表
- 计算时间比较

## 💾 结果保存

系统支持多种格式的结果保存：

```python
# 保存所有结果
planner.save_results("results_folder")

# 自动保存的文件：
# - velocity_field.xlsx      # 速度场数据
# - pressure_field.xlsx      # 压力场数据
# - path_*.xlsx             # 各算法路径数据
# - performance_metrics.xlsx # 性能指标
```

## 🔍 故障排除

### 常见问题

1. **模块导入失败**
   - 确保所有模块文件在同一目录
   - 检查Python环境和依赖包

2. **流体模拟不收敛**
   - 调整松弛时间tau（建议0.5-1.5）
   - 增加最大迭代次数
   - 降低入口速度

3. **路径规划失败**
   - 检查起点和终点是否在流体区域
   - 确保存在可行路径
   - 调整算法参数

4. **可视化问题**
   - 确保matplotlib正确安装
   - 检查字体设置
   - 更新图形驱动

## 📚 依赖包

```
numpy >= 1.19.0
matplotlib >= 3.3.0
scipy >= 1.5.0
pandas >= 1.1.0
```

## 🤝 贡献

欢迎提交问题报告和改进建议！

## 📄 许可证

本项目采用MIT许可证。

---

**流体路径规划系统 v2.0** - 让路径规划更智能，让流体模拟更高效！
