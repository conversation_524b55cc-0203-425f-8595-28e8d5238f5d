import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import time
import multiprocessing as mp
from functools import partial
import os

# 创建自定义颜色映射，用于更好的可视化效果
def create_custom_colormap():
    # 创建自定义颜色映射，从蓝色到红色，中间经过白色
    colors = [(0, 0, 0.8), (0, 0.5, 1), (0.9, 0.9, 1), (1, 0.5, 0), (0.8, 0, 0)]
    return LinearSegmentedColormap.from_list('custom_cmap', colors, N=256)

# D2Q9模型的速度集
c = np.array([
    [0, 0],    # 静止
    [1, 0],    # 东
    [0, 1],    # 北
    [-1, 0],   # 西
    [0, -1],   # 南
    [1, 1],    # 东北
    [-1, 1],   # 西北
    [-1, -1],  # 西南
    [1, -1]    # 东南
])

# D2Q9权重
w = np.array([4/9, 1/9, 1/9, 1/9, 1/9, 1/36, 1/36, 1/36, 1/36])

# 反向索引，用于反弹边界条件
opposite = np.array([0, 3, 4, 1, 2, 7, 8, 5, 6])

def add_boundary(grid_map):
    """
    在地图四周添加障碍物边界，并在左上角和右下角创建相同大小的入口和出口区域
    根据地图尺寸动态调整入口和出口区域的大小，并确保入口和出口与真实地图有足够的接触面积
    """
    h, w = grid_map.shape
    
    # 计算扩展区域的大小（取网格尺寸的10%和3的较大值）
    extension_size = max(int(min(h, w) * 0.1), 1)

    # 创建新的扩展地图，包括原始区域、扩展区域和边界
    new_h = h + 2 + 2 * extension_size  # 额外的2是为了边界，2*extension_size是为了入口和出口
    new_w = w + 2 + 2 * extension_size
    new_map = np.ones((new_h, new_w))  # 初始化为边界（1）
    
    # 将原始地图复制到新地图的中心区域
    new_map[extension_size+1:-extension_size-1, extension_size+1:-extension_size-1] = grid_map
    
    # 创建左上角入口扩展区域（对角线形式）
    for i in range(2 * extension_size + 1):
        for j in range(2 * extension_size + 1):
            # 创建对角线形式的入口，只有在对角线附近的点才设为流体
            if i == j or abs(i - j) <= extension_size // 2:
                new_map[i, j] = 0
    
    # 创建右下角出口扩展区域（对角线形式）
    for i in range(new_h - 2 * extension_size - 1, new_h):
        for j in range(new_w - 2 * extension_size - 1, new_w):
            # 创建对角线形式的出口，只有在对角线附近的点才设为流体
            # 使用相对坐标计算对角线位置
            rel_i = i - (new_h - 2 * extension_size - 1)
            rel_j = j - (new_w - 2 * extension_size - 1)
            if rel_i == rel_j or abs(rel_i - rel_j) <= extension_size // 2:
                new_map[i, j] = 0
    
    return new_map

def equilibrium(rho, u, v):
    """
    计算平衡分布函数（向量化版本）
    
    参数:
    rho: 密度场（一维数组）
    u, v: 速度场分量（一维数组）
    """
    # 预计算平方项
    usqr = u**2 + v**2
    
    # 初始化平衡分布函数数组
    feq = np.zeros((9, len(rho)))
    
    # 向量化计算所有方向的平衡分布
    for i in range(9):
        # 计算速度点积 c_i·u
        cu = c[i,0]*u + c[i,1]*v
        # 平衡分布函数公式
        feq[i] = w[i]*rho*(1 + 3*cu + 4.5*cu**2 - 1.5*usqr)
    
    return feq

def bounce_back_optimized(f, grid_map):
    """
    处理障碍物边界的反弹边界条件（优化版本）
    使用预计算的反向索引和布尔掩码进行向量化操作
    """
    # 创建障碍物掩码
    obstacle_mask = (grid_map == 1)
    
    # 对每个方向应用反弹规则
    for i in range(1, 9):  # 跳过静止方向
        opp_i = opposite[i]  # 获取反向索引
        # 在障碍物位置交换分布函数
        temp = f[i].copy()
        f[i, obstacle_mask] = f[opp_i, obstacle_mask]
        f[opp_i, obstacle_mask] = temp[obstacle_mask]
    
    return f

def apply_zou_he_boundary(f, rho, u, v, grid_map, inlet_velocity):
    """
    应用Zou-He边界条件，比简单的速度设置更准确
    
    参数:
    f: 分布函数
    rho, u, v: 宏观量
    grid_map: 网格地图
    inlet_velocity: 入口速度
    """
    nx, ny = grid_map.shape
    
    # 计算扩展区域的大小，与add_boundary函数保持一致
    h, w = grid_map.shape
    extension_size = max(int(min(h, w) * 0.1), 3)
    
    # 入口边界条件（左上角区域）- 速度边界
    # 检测入口位置
    inlet_mask = np.zeros_like(grid_map, dtype=bool)
    inlet_mask[0, :extension_size+1] = True  # 左边界
    inlet_mask[:extension_size+1, 0] = True  # 上边界
    inlet_mask = inlet_mask & (grid_map == 0)  # 只在流体区域应用
    
    # 设置入口速度
    u[inlet_mask] = inlet_velocity
    v[inlet_mask] = inlet_velocity
    
    # 出口边界条件（右下角区域）- 压力边界
    # 检测出口位置
    outlet_mask = np.zeros_like(grid_map, dtype=bool)
    outlet_mask[-1, -extension_size-1:] = True  # 右边界
    outlet_mask[-extension_size-1:, -1] = True  # 下边界
    outlet_mask = outlet_mask & (grid_map == 0)  # 只在流体区域应用
    
    # 设置出口压力（密度）
    rho[outlet_mask] = 1.0
    
    # 零梯度速度条件
    # 对于右边界
    u[-1, -extension_size-1:] = u[-2, -extension_size-1:]
    v[-1, -extension_size-1:] = v[-2, -extension_size-1:]
    # 对于下边界
    u[-extension_size-1:, -1] = u[-extension_size-1:, -2]
    v[-extension_size-1:, -1] = v[-extension_size-1:, -2]
    
    return f, rho, u, v

def simulate_flow(grid_map, max_iter=5000, tau=0.5, inlet_velocity=0.1, convergence_threshold=1e-6, 
                 convergence_window=50, use_multiprocessing=False, num_processes=None):
    """
    使用LBM模拟流动（优化版本）
    
    参数:
    grid_map: 网格地图，0表示流体，1表示障碍物
    max_iter: 最大迭代次数
    tau: 松弛时间，控制粘性
    inlet_velocity: 入口速度
    convergence_threshold: 收敛阈值
    convergence_window: 收敛判断窗口大小
    use_multiprocessing: 是否使用多进程加速
    num_processes: 进程数量，默认为None（使用CPU核心数）
    """
    # 记录开始时间
    start_time = time.time()
    
    # 初始化
    nx, ny = grid_map.shape
    f = np.ones((9, nx, ny)) / 9.0  # 均匀初始化，更稳定
    
    # 添加小的随机扰动以打破对称性
    np.random.seed(42)  # 设置随机种子以保证可重复性
    f += 0.01 * np.random.randn(9, nx, ny) / 9.0
    
    # 初始化宏观量
    rho = np.ones((nx, ny))
    u = np.zeros((nx, ny))
    v = np.zeros((nx, ny))
    
    # 记录收敛历史
    convergence_history = []
    velocity_window = []  # 用于滑动窗口收敛判断
    
    # 设置多进程
    if use_multiprocessing:
        if num_processes is None:
            num_processes = mp.cpu_count()
        print(f"使用多进程加速，进程数: {num_processes}")
        pool = mp.Pool(processes=num_processes)
    
    # 主循环
    for step in range(max_iter):
        # 保存上一步的速度场用于收敛检查
        if step > 0:
            u_old = u.copy()
            v_old = v.copy()
        
        # 计算宏观量
        rho = np.sum(f, axis=0)
        u = np.zeros_like(rho)
        v = np.zeros_like(rho)
        
        # 向量化计算速度
        for i in range(9):
            u += c[i,0] * f[i]
            v += c[i,1] * f[i]
        
        # 防止除零错误
        mask = (rho > 1e-10)
        u[mask] = u[mask] / rho[mask]
        v[mask] = v[mask] / rho[mask]
        
        # 应用边界条件
        f, rho, u, v = apply_zou_he_boundary(f, rho, u, v, grid_map, inlet_velocity)
        
        # 障碍物处的速度设为0（向量化操作）
        obstacle_mask = (grid_map == 1)
        u[obstacle_mask] = 0
        v[obstacle_mask] = 0
        
        # 计算平衡态
        rho_flat = rho.flatten()
        u_flat = u.flatten()
        v_flat = v.flatten()
        
        # 使用多进程计算平衡分布（对大规模问题有效）
        if use_multiprocessing and nx*ny > 10000:
            # 将数据分块
            chunk_size = len(rho_flat) // num_processes
            chunks = [(rho_flat[i:i+chunk_size], u_flat[i:i+chunk_size], v_flat[i:i+chunk_size]) 
                      for i in range(0, len(rho_flat), chunk_size)]
            
            # 并行计算平衡分布
            results = pool.starmap(equilibrium, chunks)
            
            # 合并结果
            feq = np.hstack(results)
        else:
            # 单进程计算
            feq = equilibrium(rho_flat, u_flat, v_flat)
        
        feq = feq.reshape((9, nx, ny))
        
        # 碰撞步骤（向量化）
        f = f - (1/tau)*(f - feq)
        
        # 流动步骤（向量化）
        for i in range(9):
            f[i] = np.roll(np.roll(f[i], c[i,0], axis=0), c[i,1], axis=1)
        
        # 处理障碍物边界
        f = bounce_back_optimized(f, grid_map)
        
        # 检查收敛性
        if step > 0:
            velocity_diff = np.sqrt(np.mean((u - u_old)**2 + (v - v_old)**2))
            convergence_history.append(velocity_diff)
            velocity_window.append(velocity_diff)
            
            # 保持窗口大小
            if len(velocity_window) > convergence_window:
                velocity_window.pop(0)
            
            # 每100步打印一次状态
            if step % 100 == 0:
                elapsed = time.time() - start_time
                print(f"Iteration {step}, Velocity change: {velocity_diff:.8f}, Time: {elapsed:.2f}s")
            
            # 使用滑动窗口判断收敛
            if len(velocity_window) == convergence_window:
                window_mean = np.mean(velocity_window)
                window_std = np.std(velocity_window)
                
                # 如果平均变化小于阈值且标准差小，认为收敛
                if window_mean < convergence_threshold and window_std < convergence_threshold/2 and step > 1000:
                    elapsed = time.time() - start_time
                    print(f"Converged after {step} iterations, Time: {elapsed:.2f}s")
                    break
    
    # 关闭进程池
    if use_multiprocessing:
        pool.close()
        pool.join()
    
    # 计算速度大小和涡量（向量化）
    speed = np.sqrt(u**2 + v**2)
    
    # 计算涡量（使用中心差分，向量化）
    vorticity = np.zeros_like(u)
    # 内部点使用中心差分
    vorticity[1:-1, 1:-1] = (v[2:, 1:-1] - v[:-2, 1:-1]) - (u[1:-1, 2:] - u[1:-1, :-2])
    
    return u, v, rho, speed, vorticity, convergence_history

def calculate_reynolds_number(u_max, characteristic_length, tau):
    """
    计算雷诺数
    
    参数:
    u_max: 最大速度
    characteristic_length: 特征长度（通常为通道宽度）
    tau: 松弛时间
    """
    # LBM中的运动粘度与松弛时间的关系: nu = (tau - 0.5) / 3
    nu = (tau - 0.5) / 3.0
    return u_max * characteristic_length / nu

def load_excel_data(file_path='label/sea_ice_340.xlsx'):
    """
    从Excel文件加载数据并转换为二进制地图
    """
    try:
        import pandas as pd
        data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
        numpy_array = data.to_numpy()
        # 转换为二进制地图：0表示流体，1表示障碍物
        binary_map = np.where(numpy_array == 0, 255, np.where(numpy_array == 255, 0, numpy_array))
        binary_map = binary_map / 255  # 归一化到0-1范围
        binary_map = binary_map.astype(int)  # 转换为整数
        #binary_map = numpy_array.astype(int)  # 转换为整数
        return binary_map
    except Exception as e:
        print(f"Error loading Excel data: {e}")
        return None

def visualize_results(grid_map, u, v, rho, speed, vorticity, convergence_history):
    """
    可视化模拟结果，包括速度场、压力场、涡量场、速度矢量场、流线图和收敛历史
    """
    # 创建自定义颜色映射
    custom_cmap = create_custom_colormap()
    
    # 创建图形
    fig = plt.figure(figsize=(20, 12))
    
    # 1. 速度场
    ax1 = fig.add_subplot(231)
    im1 = ax1.imshow(speed, cmap=custom_cmap,vmin=0,vmax=0.05)
    plt.colorbar(im1, ax=ax1, label='Speed')
    ax1.imshow(grid_map, cmap='gray_r', alpha=0.3)
    ax1.set_title('Velocity Magnitude')
    
    import os
    import pandas as pd
    # 保存为 Excel
    base_path = "C:/Users/<USER>/Desktop/ICE/lable"
    output_excel_path = os.path.join(base_path, "Velocity.xlsx")
    df = pd.DataFrame(speed)
    os.makedirs(os.path.dirname(output_excel_path), exist_ok=True)
    df.to_excel(output_excel_path, index=False, header=False)
    print(f"处理完成，结果已保存至 {output_excel_path}")

    # 2. 压力场（用密度表示）
    ax2 = fig.add_subplot(232)
    im2 = ax2.imshow(rho, cmap='coolwarm',vmin=-abs(rho).max(), vmax=abs(rho).max())
    plt.colorbar(im2, ax=ax2, label='Density')
    ax2.imshow(grid_map, cmap='gray_r', alpha=0.3)
    ax2.set_title('Pressure Field')
    
    # 3. 涡量场
    ax3 = fig.add_subplot(233)
    # 使用对称的颜色映射，使零值为白色
    im3 = ax3.imshow(vorticity, cmap='seismic', vmin=-abs(vorticity).max(), vmax=abs(vorticity).max())
    plt.colorbar(im3, ax=ax3, label='Vorticity')
    ax3.set_title('Vorticity Field')
    
    # 4. 速度矢量场
    ax4 = fig.add_subplot(234)
    skip = max(1, grid_map.shape[0] // 50)  # 动态调整跳过的点数，使图更清晰
    x, y = np.meshgrid(np.arange(0, grid_map.shape[1]), np.arange(0, grid_map.shape[0]))
    quiver = ax4.quiver(x[::skip, ::skip], y[::skip, ::skip], 
                      u[::skip, ::skip], v[::skip, ::skip],
                      speed[::skip, ::skip], cmap=custom_cmap, scale=15)
    plt.colorbar(quiver, ax=ax4, label='Speed')
    ax4.imshow(grid_map, cmap='gray', alpha=0.3)
    ax4.set_title('Velocity Vectors')
    
    # 5. 流线图
    ax5 = fig.add_subplot(235)
    streamplot = ax5.streamplot(x, y, u, v, density=1.5, color=speed, cmap=custom_cmap, linewidth=1.5)
    plt.colorbar(streamplot.lines, ax=ax5, label='Speed')
    ax5.imshow(grid_map, cmap='gray', alpha=0.3)
    ax5.set_title('Streamlines')
    
    # 6. 收敛历史
    ax6 = fig.add_subplot(236)
    ax6.semilogy(convergence_history)
    ax6.set_xlabel('Iteration')
    ax6.set_ylabel('Velocity Change (log scale)')
    ax6.set_title('Convergence History')
    ax6.grid(True)
    
    plt.tight_layout()
    return fig


def main():
    """
    主函数：加载数据，运行模拟，可视化结果
    """
    # 记录总运行时间
    total_start_time = time.time()
    
    # 尝试从Excel加载数据
    excel_data = load_excel_data()
    
    if excel_data is not None:
        grid_map = excel_data
        print("Using data from Excel file")
    else:
        # 使用示例网格地图
        grid_map = np.array([
            [0, 0, 0, 0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0, 1, 0, 0],
            [0, 1, 0, 0, 1, 0, 1, 0],
            [0, 1, 1, 1, 0, 0, 0, 0],
            [0, 0, 0, 1, 0, 1, 1, 0],
            [0, 1, 0, 0, 0, 0, 0, 0],
            [0, 0, 1, 1, 0, 1, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0]
        ])
        print("Using example grid map")
    
    # 添加边界
    grid_map = add_boundary(grid_map)
    # 显示原始地图
    plt.figure(figsize=(8, 8))
    plt.imshow(grid_map, cmap='gray')
    plt.title("Grid Map with Boundary")
    plt.colorbar(label='Obstacle (1) / Fluid (0)')
    plt.show()
    
    print("Starting LBM simulation...")
    print(f"Grid size: {grid_map.shape[0]}x{grid_map.shape[1]}")
    
    # 设置模拟参数
    tau = 1  # 松弛时间
    inlet_velocity =0.3 # 入口速度
    max_iter = 10000  # 最大迭代次数
    convergence_threshold = 1e-5 # 收敛阈值
    
    # 检测是否可以使用多进程
    use_multiprocessing = False
    if grid_map.size > 10000:  # 对于大规模问题，考虑使用多进程
        use_multiprocessing = True
        print("Large grid detected, enabling multiprocessing")
    
    # 模拟流动
    u, v, rho, speed, vorticity, convergence_history = simulate_flow(
        grid_map, 
        max_iter=max_iter, 
        tau=tau, 
        inlet_velocity=inlet_velocity, 
        convergence_threshold=convergence_threshold,
        use_multiprocessing=use_multiprocessing
    )
    
    # 计算雷诺数
    characteristic_length = min(grid_map.shape) / 2  # 使用网格尺寸的一半作为特征长度
    re = calculate_reynolds_number(np.max(speed), characteristic_length, tau)
    print(f"Estimated Reynolds number: {re:.2f}")
    
    # 可视化结果
    fig = visualize_results(grid_map, u, v, rho, speed, vorticity, convergence_history)
    
    # 保存结果图像
    #fig.savefig('lbm_simulation_results.png', dpi=300, bbox_inches='tight')
    
    # 显示图像
    plt.show()
    
    # 打印总运行时间
    total_elapsed = time.time() - total_start_time
    print(f"Total execution time: {total_elapsed:.2f} seconds")

if __name__ == "__main__":
    main()