"""
流体路径规划系统 - 完整示例入口文件
包含所有功能和超参数调整选项，便于后续使用

使用方法:
1. 直接运行: python complete_example.py
2. 修改参数: 在main()函数中调整各种参数
3. 自定义场景: 修改create_custom_scenario()函数
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from fluid_path_planner import FluidPathPlanner
    from utils import DataProcessor, GridProcessor
    print("✓ 模块导入成功")
except ImportError as e:
    print(f"✗ 模块导入失败: {e}")
    print("请确保src文件夹中包含所有必要的模块文件")
    exit(1)

# 设置matplotlib中文字体
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12


def create_custom_scenario(scenario_type="complex"):
    """
    创建自定义测试场景

    参数:
    scenario_type: 场景类型
        - "simple": 简单矩形障碍物
        - "complex": 复杂多障碍物
        - "channel": 通道流动
        - "maze": 迷宫结构
        - "custom": 自定义设计

    返回:
    grid_map: 网格地图 (0=流体, 1=障碍物)
    """

    if scenario_type == "simple":
        # 简单矩形障碍物
        grid = np.zeros((20, 30), dtype=int)
        grid[8:12, 10:20] = 1  # 中央矩形障碍物

    elif scenario_type == "complex":
        # 复杂多障碍物场景
        grid = np.zeros((30, 40), dtype=int)

        # 添加多个圆形障碍物
        centers = [(8, 10), (15, 25), (22, 15), (10, 30)]
        for cx, cy in centers:
            for i in range(grid.shape[0]):
                for j in range(grid.shape[1]):
                    if (i - cx)**2 + (j - cy)**2 <= 16:  # 半径4的圆
                        grid[i, j] = 1

        # 添加矩形障碍物
        grid[5:8, 15:25] = 1
        grid[20:25, 5:15] = 1

    elif scenario_type == "channel":
        # 通道流动场景
        grid = np.zeros((25, 50), dtype=int)

        # 创建收缩通道
        for i in range(grid.shape[0]):
            # 上下边界
            if i < 5 or i > 19:
                grid[i, 15:35] = 1
            # 中间收缩部分
            elif 10 <= i <= 14:
                grid[i, 20:30] = 1

    elif scenario_type == "maze":
        # 迷宫结构
        grid = np.zeros((25, 25), dtype=int)

        # 创建迷宫墙壁
        grid[5:20, 5] = 1      # 左墙
        grid[5:20, 20] = 1     # 右墙
        grid[5, 5:15] = 1      # 上墙
        grid[15, 10:20] = 1    # 下墙
        grid[10, 8:12] = 1     # 中间墙

    elif scenario_type == "custom":
        # 自定义场景 - 您可以在这里设计自己的障碍物布局
        grid = np.zeros((25, 35), dtype=int)

        # 示例: L形障碍物
        grid[8:15, 10:12] = 1   # 垂直部分
        grid[13:15, 10:20] = 1  # 水平部分

        # 示例: 圆形障碍物
        center_x, center_y = 18, 25
        radius = 3
        for i in range(grid.shape[0]):
            for j in range(grid.shape[1]):
                if (i - center_x)**2 + (j - center_y)**2 <= radius**2:
                    grid[i, j] = 1

    else:
        raise ValueError(f"未知的场景类型: {scenario_type}")

    return grid


def load_excel_scenario(file_path):
    """
    从Excel文件加载场景

    参数:
    file_path: Excel文件路径

    返回:
    grid_map: 网格地图
    """
    try:
        grid_data = DataProcessor.load_excel_data(file_path)
        if grid_data is not None:
            print(f"✓ 成功从Excel加载场景: {file_path}")
            print(f"  网格大小: {grid_data.shape}")
            return grid_data
        else:
            print(f"✗ Excel文件加载失败: {file_path}")
            return None
    except Exception as e:
        print(f"✗ Excel文件加载出错: {e}")
        return None


def run_complete_simulation(grid_map, config):
    """
    运行完整的流体路径规划仿真

    参数:
    grid_map: 网格地图
    config: 配置参数字典

    返回:
    planner: 流体路径规划器对象
    """

    print(f"\n{'='*60}")
    print(f"开始流体路径规划仿真")
    print(f"{'='*60}")

    # 创建流体路径规划器
    planner = FluidPathPlanner(
        grid_map,
        start_point=config.get('start_point'),
        end_point=config.get('end_point')
    )

    print(f"原始网格大小: {grid_map.shape}")

    # 1. 准备网格边界
    print("\n1. 准备网格边界...")
    planner.prepare_grid_with_inlet_outlet(
        boundary_type=config['boundary_type']
    )
    print(f"   扩展后网格大小: {planner.grid_map.shape}")
    print(f"   入口位置: {planner.start_point}")
    print(f"   出口位置: {planner.end_point}")

    # 2. 可视化初始网格
    if config['show_initial_grid']:
        print("\n2. 可视化初始网格...")
        try:
            grid_fig = planner.visualizer.visualize_grid_with_boundary(
                planner.grid_map, planner.start_point, planner.end_point)
            plt.title("Initial Grid with Inlet/Outlet")
            if config['save_results']:
                plt.savefig(f"{config['output_dir']}/initial_grid.png", dpi=300, bbox_inches='tight')
            if config['show_plots']:
                plt.show()
            else:
                plt.close()
        except Exception as e:
            print(f"   网格可视化出错: {e}")

    # 3. 运行LBM流体模拟
    print("\n3. 运行LBM流体模拟...")
    print(f"   参数设置:")
    print(f"     最大迭代次数: {config['lbm_params']['max_iter']}")
    print(f"     松弛时间: {config['lbm_params']['tau']}")
    print(f"     入口速度: {config['lbm_params']['inlet_velocity']}")
    print(f"     收敛阈值: {config['lbm_params']['convergence_threshold']}")
    print(f"     碰撞模型: {config['lbm_params']['collision_model']}")
    print(f"     边界条件: {config['lbm_params']['boundary_condition']}")

    try:
        start_time = time.time()
        planner.run_fluid_simulation(**config['lbm_params'])
        simulation_time = time.time() - start_time
        print(f"   ✓ 流体模拟完成，耗时: {simulation_time:.2f}秒")
    except Exception as e:
        print(f"   ✗ 流体模拟失败: {e}")
        return None

    # 4. 可视化流体场
    if config['show_fluid_fields']:
        print("\n4. 可视化流体场...")
        try:
            save_path = f"{config['output_dir']}/fluid_fields.png" if config['save_results'] else None
            fluid_fig = planner.visualize_fluid_fields(save_path=save_path)
            if config['show_plots']:
                plt.show()
            else:
                plt.close()
        except Exception as e:
            print(f"   流体场可视化出错: {e}")

    # 5. 3D可视化
    if config['show_3d_visualization']:
        print("\n5. 3D可视化...")
        try:
            for field_name in config['3d_fields']:
                speed_3d_fig = planner.visualize_3d_fields(field_name)
                if config['save_results']:
                    speed_3d_fig.savefig(f"{config['output_dir']}/3d_{field_name}.png",
                                        dpi=300, bbox_inches='tight')
                if config['show_plots']:
                    plt.show()
                else:
                    plt.close()
        except Exception as e:
            print(f"   3D可视化出错: {e}")

    # 6. 运行路径规划算法
    print("\n6. 运行路径规划算法...")

    paths = {}
    metrics = {}

    # 运行选定的算法
    if 'astar_classic' in config['algorithms']:
        print("   运行经典A*算法...")
        path = planner.find_path_astar_classic(
            allow_diagonal=config['path_params']['allow_diagonal']
        )
        if path:
            print(f"     ✓ A*路径长度: {len(path)}")

    if 'dijkstra' in config['algorithms']:
        print("   运行Dijkstra算法...")
        path = planner.find_path_dijkstra(
            allow_diagonal=config['path_params']['allow_diagonal']
        )
        if path:
            print(f"     ✓ Dijkstra路径长度: {len(path)}")

    if 'fluid_astar' in config['algorithms']:
        print("   运行流体增强A*算法...")
        path = planner.find_path_fluid_astar(
            fluid_weight=config['path_params']['fluid_weight'],
            allow_diagonal=config['path_params']['allow_diagonal']
        )
        if path:
            print(f"     ✓ 流体A*路径长度: {len(path)}")

    if 'velocity_field' in config['algorithms']:
        print("   运行速度场跟随算法...")
        path = planner.find_path_velocity_field(
            step_size=config['path_params']['step_size'],
            max_steps=config['path_params']['max_steps']
        )
        if path:
            print(f"     ✓ 速度场路径长度: {len(path)}")

    # 获取所有路径和指标
    paths = planner.paths.copy()
    metrics = planner.path_metrics.copy()

    print(f"   总共找到 {len(paths)} 条路径")

    # 7. 可视化路径比较
    if config['show_path_comparison'] and paths:
        print("\n7. 可视化路径比较...")
        try:
            save_path = f"{config['output_dir']}/path_comparison.png" if config['save_results'] else None
            path_fig = planner.visualize_paths(save_path=save_path)
            if config['show_plots']:
                plt.show()
            else:
                plt.close()
        except Exception as e:
            print(f"   路径比较可视化出错: {e}")

    # 8. 显示性能指标
    if config['show_performance_metrics'] and metrics:
        print("\n8. 性能指标比较...")
        try:
            save_path = f"{config['output_dir']}/performance_metrics.png" if config['save_results'] else None
            perf_fig = planner.visualizer.plot_performance_metrics(metrics, save_path=save_path)
            if config['show_plots']:
                plt.show()
            else:
                plt.close()

            # 打印详细指标
            print("   详细性能指标:")
            for alg_name, alg_metrics in metrics.items():
                print(f"\n   {alg_name}:")
                for metric, value in alg_metrics.items():
                    if isinstance(value, float):
                        print(f"     {metric}: {value:.6f}")
                    else:
                        print(f"     {metric}: {value}")

        except Exception as e:
            print(f"   性能指标显示出错: {e}")

    # 9. 保存结果
    if config['save_results']:
        print(f"\n9. 保存结果到: {config['output_dir']}")
        try:
            planner.save_results(config['output_dir'])

            # 保存配置文件
            import json
            config_file = os.path.join(config['output_dir'], 'simulation_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                # 转换numpy类型为Python原生类型以便JSON序列化
                json_config = {}
                for key, value in config.items():
                    if isinstance(value, dict):
                        json_config[key] = {k: float(v) if isinstance(v, np.floating) else v
                                          for k, v in value.items()}
                    else:
                        json_config[key] = value
                json.dump(json_config, f, indent=2, ensure_ascii=False)

            print("   ✓ 所有结果已保存")
        except Exception as e:
            print(f"   结果保存出错: {e}")

    # 10. 显示总结
    print(f"\n{'='*60}")
    print(f"仿真完成总结:")
    print(f"  网格大小: {planner.grid_map.shape}")
    print(f"  入口位置: {planner.start_point}")
    print(f"  出口位置: {planner.end_point}")
    print(f"  测试算法: {list(paths.keys())}")
    print(f"  找到路径数: {len(paths)}")
    print(f"  仿真总时间: {simulation_time:.2f}秒")
    if config['save_results']:
        print(f"  结果保存位置: {os.path.abspath(config['output_dir'])}")
    print(f"{'='*60}")

    return planner


def get_default_config():
    """
    获取默认配置参数

    返回:
    config: 配置参数字典
    """

    config = {
        # ==================== 基本设置 ====================
        'scenario_type': 'complex',        # 场景类型: simple, complex, channel, maze, custom
        'excel_file': None,                # Excel文件路径 (如果使用Excel数据)
        'start_point': None,               # 起点坐标 (None表示自动确定)
        'end_point': None,                 # 终点坐标 (None表示自动确定)
        'boundary_type': 'diagonal',       # 边界类型: diagonal, rectangular, custom

        # ==================== LBM仿真参数 ====================
        'lbm_params': {
            'max_iter': 3000,              # 最大迭代次数
            'tau': 0.8,                    # 松弛时间 (0.5-2.0, 影响粘性)
            'inlet_velocity': 0.1,         # 入口速度 (0.01-0.2)
            'convergence_threshold': 1e-6, # 收敛阈值
            'collision_model': 'bgk',      # 碰撞模型: bgk, mrt
            'boundary_condition': 'zou_he' # 边界条件: zou_he, equilibrium
        },

        # ==================== 路径规划参数 ====================
        'algorithms': [                    # 要运行的算法列表
            'astar_classic',               # 经典A*算法
            'dijkstra',                    # Dijkstra算法
            'fluid_astar',                 # 流体增强A*算法
            'velocity_field'               # 速度场跟随算法
        ],

        'path_params': {
            'allow_diagonal': True,        # 是否允许对角线移动
            'fluid_weight': 0.7,          # 流体信息权重 (0.0-1.0)
            'step_size': 0.5,             # 速度场跟随步长
            'max_steps': 10000            # 速度场跟随最大步数
        },

        # ==================== 可视化设置 ====================
        'show_initial_grid': True,        # 显示初始网格
        'show_fluid_fields': True,        # 显示流体场
        'show_3d_visualization': True,     # 显示3D可视化
        'show_path_comparison': True,      # 显示路径比较
        'show_performance_metrics': True,  # 显示性能指标
        'show_plots': True,               # 是否显示图表 (False则只保存)

        '3d_fields': ['speed', 'pressure'], # 3D可视化的场: speed, pressure, vorticity

        # ==================== 输出设置 ====================
        'save_results': True,             # 是否保存结果
        'output_dir': 'simulation_output', # 输出目录

        # ==================== 高级参数 ====================
        'advanced_params': {
            'reynolds_number_target': None, # 目标雷诺数 (None表示不调整)
            'grid_refinement': 1,          # 网格细化倍数
            'adaptive_tau': False,         # 是否使用自适应松弛时间
            'turbulence_model': None       # 湍流模型 (None, 'smagorinsky')
        }
    }

    return config


def main():
    """
    主函数 - 程序入口点
    您可以在这里修改参数来自定义仿真
    """

    print("流体路径规划系统 - 完整示例")
    print("="*60)

    # ==================== 获取配置参数 ====================
    config = get_default_config()

    # ==================== 自定义参数区域 ====================
    # 在这里修改您需要的参数

    # 1. 场景设置
    config['scenario_type'] = 'complex'    # 可选: simple, complex, channel, maze, custom
    # config['excel_file'] = 'label/sea_ice_281.xlsx'  # 取消注释以使用Excel文件

    # 2. LBM参数调整
    config['lbm_params']['max_iter'] = 2000        # 减少迭代次数以加快演示
    config['lbm_params']['tau'] = 0.8              # 松弛时间
    config['lbm_params']['inlet_velocity'] = 0.1   # 入口速度
    config['lbm_params']['collision_model'] = 'bgk' # 碰撞模型

    # 3. 路径规划算法选择
    config['algorithms'] = [
        'astar_classic',
        'dijkstra',
        'fluid_astar',
        'velocity_field'
    ]

    # 4. 路径规划参数
    config['path_params']['fluid_weight'] = 0.7    # 流体权重
    config['path_params']['allow_diagonal'] = True # 允许对角线移动

    # 5. 可视化设置
    config['show_plots'] = True           # 是否显示图表
    config['save_results'] = True         # 是否保存结果
    config['output_dir'] = 'my_simulation' # 输出目录名

    # 6. 高级参数 (可选)
    # config['advanced_params']['reynolds_number_target'] = 100  # 目标雷诺数
    # config['advanced_params']['adaptive_tau'] = True           # 自适应松弛时间

    # ==================== 创建输出目录 ====================
    if config['save_results']:
        os.makedirs(config['output_dir'], exist_ok=True)
        print(f"结果将保存到: {os.path.abspath(config['output_dir'])}")

    # ==================== 加载或创建场景 ====================
    if config['excel_file'] and os.path.exists(config['excel_file']):
        # 从Excel文件加载
        grid_map = load_excel_scenario(config['excel_file'])
        if grid_map is None:
            print("Excel加载失败，使用默认场景")
            grid_map = create_custom_scenario(config['scenario_type'])
    else:
        # 创建自定义场景
        print(f"创建场景类型: {config['scenario_type']}")
        grid_map = create_custom_scenario(config['scenario_type'])

    print(f"场景网格大小: {grid_map.shape}")
    print(f"障碍物数量: {np.sum(grid_map == 1)}")
    print(f"流体区域: {np.sum(grid_map == 0)}")

    # ==================== 运行完整仿真 ====================
    try:
        planner = run_complete_simulation(grid_map, config)

        if planner is not None:
            print("\n🎉 仿真成功完成！")

            # 显示最终统计信息
            summary = planner.get_performance_summary()
            print(f"\n最终统计:")
            print(f"  算法数量: {summary['total_paths_found']}")
            print(f"  成功算法: {summary['algorithms_tested']}")

            if config['save_results']:
                print(f"\n📁 所有结果已保存到: {os.path.abspath(config['output_dir'])}")
                print(f"   包含: 图像文件、数据文件、性能报告、配置文件")

            return planner
        else:
            print("\n❌ 仿真失败")
            return None

    except KeyboardInterrupt:
        print("\n⏹️ 仿真被用户中断")
        return None
    except Exception as e:
        print(f"\n💥 仿真过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def quick_test():
    """
    快速测试函数 - 用于验证系统功能
    """
    print("运行快速测试...")

    # 简化配置
    config = get_default_config()
    config['scenario_type'] = 'simple'
    config['lbm_params']['max_iter'] = 500
    config['algorithms'] = ['astar_classic', 'fluid_astar']
    config['show_plots'] = False
    config['save_results'] = False
    config['show_3d_visualization'] = False

    # 创建简单场景
    grid_map = create_custom_scenario('simple')

    # 运行测试
    planner = run_complete_simulation(grid_map, config)

    if planner is not None:
        print("✅ 快速测试通过")
        return True
    else:
        print("❌ 快速测试失败")
        return False


if __name__ == "__main__":
    # 选择运行模式
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        # 快速测试模式
        quick_test()
    else:
        # 完整仿真模式
        main()
