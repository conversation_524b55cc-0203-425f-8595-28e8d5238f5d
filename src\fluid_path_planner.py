"""
流体路径规划主类
集成LBM模拟、路径规划算法和可视化功能
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional, Dict, Any
import time

from lbm_core import LBMCore
from path_planning import PathPlanningAlgorithms
from visualization import FluidVisualization
from utils import GridProcessor, DataProcessor, PerformanceAnalyzer, FluidAnalyzer


class FluidPathPlanner:
    """
    流体路径规划器主类
    集成了LBM流体模拟和多种路径规划算法
    """

    def __init__(self, grid_map: np.ndarray,
                 start_point: Optional[Tuple[int, int]] = None,
                 end_point: Optional[Tuple[int, int]] = None,
                 lbm_model: str = 'D2Q9'):
        """
        初始化流体路径规划器

        参数:
        grid_map: 网格地图，0表示流体，1表示障碍物
        start_point: 起点坐标
        end_point: 终点坐标
        lbm_model: LBM模型类型
        """
        self.original_grid = grid_map.copy()
        self.grid_map = None
        self.start_point = start_point
        self.end_point = end_point

        # 初始化组件
        self.lbm_core = None
        self.path_planner = None
        self.visualizer = FluidVisualization()
        self.performance_analyzer = PerformanceAnalyzer()

        # 流体场数据
        self.u = None
        self.v = None
        self.rho = None
        self.speed = None
        self.vorticity = None
        self.convergence_history = []

        # 路径数据
        self.paths = {}
        self.path_metrics = {}

        print(f"流体路径规划器初始化完成")
        print(f"原始网格大小: {grid_map.shape}")

    def prepare_grid_with_inlet_outlet(self, boundary_type: str = 'diagonal') -> None:
        """
        准备带有入口和出口的网格

        参数:
        boundary_type: 边界类型
        """
        print("准备网格边界...")

        # 添加边界
        self.grid_map = GridProcessor.add_boundary(self.original_grid, boundary_type)

        # 自动确定入口和出口位置
        if self.start_point is None or self.end_point is None:
            inlet_pos, outlet_pos = GridProcessor.find_inlet_outlet_positions(self.grid_map)
            if self.start_point is None:
                self.start_point = inlet_pos
            if self.end_point is None:
                self.end_point = outlet_pos

        # 初始化LBM核心和路径规划器
        self.lbm_core = LBMCore(self.grid_map.shape)
        self.path_planner = PathPlanningAlgorithms(self.grid_map)

        print(f"扩展后网格大小: {self.grid_map.shape}")
        print(f"入口位置: {self.start_point}")
        print(f"出口位置: {self.end_point}")

    def run_fluid_simulation(self, max_iter: int = 5000, tau: float = 0.8,
                           inlet_velocity: float = 0.1, convergence_threshold: float = 1e-6,
                           collision_model: str = 'bgk', boundary_condition: str = 'zou_he') -> None:
        """
        运行LBM流体模拟

        参数:
        max_iter: 最大迭代次数
        tau: 松弛时间
        inlet_velocity: 入口速度
        convergence_threshold: 收敛阈值
        collision_model: 碰撞模型 ('bgk', 'mrt')
        boundary_condition: 边界条件类型
        """
        if self.lbm_core is None:
            raise ValueError("请先调用 prepare_grid_with_inlet_outlet() 方法")

        print("开始LBM流体模拟...")
        self.performance_analyzer.start_timer('fluid_simulation')

        # 初始化分布函数
        self.lbm_core.f = np.ones((self.lbm_core.q, self.lbm_core.nx, self.lbm_core.ny)) / self.lbm_core.q

        # 添加小的随机扰动
        np.random.seed(42)
        self.lbm_core.f += 0.01 * np.random.randn(self.lbm_core.q, self.lbm_core.nx, self.lbm_core.ny) / self.lbm_core.q

        # 主循环
        convergence_window = []
        window_size = 50

        for step in range(max_iter):
            # 保存上一步的速度场
            if step > 0:
                u_old = self.lbm_core.u.copy()
                v_old = self.lbm_core.v.copy()

            # 计算宏观量
            self.lbm_core.compute_macroscopic()

            # 应用边界条件
            self.lbm_core.apply_boundary_conditions(self.grid_map, inlet_velocity, boundary_condition)

            # 障碍物处速度设为0
            obstacle_mask = (self.grid_map == 1)
            self.lbm_core.u[obstacle_mask] = 0
            self.lbm_core.v[obstacle_mask] = 0

            # 碰撞步骤
            if collision_model == 'bgk':
                self.lbm_core.collision_bgk(tau)
            elif collision_model == 'mrt':
                self.lbm_core.collision_mrt(tau)
            else:
                raise ValueError(f"不支持的碰撞模型: {collision_model}")

            # 流动步骤
            self.lbm_core.streaming()

            # 处理障碍物边界
            self.lbm_core.bounce_back(obstacle_mask)

            # 检查收敛性
            if step > 0:
                velocity_diff = np.sqrt(np.mean((self.lbm_core.u - u_old)**2 + (self.lbm_core.v - v_old)**2))
                self.convergence_history.append(velocity_diff)
                convergence_window.append(velocity_diff)

                # 保持窗口大小
                if len(convergence_window) > window_size:
                    convergence_window.pop(0)

                # 打印进度
                if step % 100 == 0:
                    print(f"迭代 {step}, 速度变化: {velocity_diff:.8f}")

                # 收敛判断
                if len(convergence_window) == window_size:
                    window_mean = np.mean(convergence_window)
                    window_std = np.std(convergence_window)

                    if window_mean < convergence_threshold and window_std < convergence_threshold/2 and step > 1000:
                        print(f"在第 {step} 次迭代后收敛")
                        break

        # 计算最终的流体场
        self.u = self.lbm_core.u.copy()
        self.v = self.lbm_core.v.copy()
        self.rho = self.lbm_core.rho.copy()
        self.speed = np.sqrt(self.u**2 + self.v**2)
        self.vorticity = FluidAnalyzer.calculate_vorticity(self.u, self.v)

        simulation_time = self.performance_analyzer.end_timer('fluid_simulation')

        # 计算雷诺数
        characteristic_length = min(self.grid_map.shape) / 2
        reynolds_number = self.performance_analyzer.calculate_reynolds_number(
            np.max(self.speed), characteristic_length, tau)

        print(f"流体模拟完成，耗时: {simulation_time:.2f}秒")
        print(f"雷诺数: {reynolds_number:.2f}")

        # 分析收敛性能
        convergence_metrics = self.performance_analyzer.analyze_convergence(self.convergence_history)
        self.performance_analyzer.metrics.update(convergence_metrics)

    def find_path_astar_classic(self, allow_diagonal: bool = True) -> Optional[List[Tuple[int, int]]]:
        """使用经典A*算法寻找路径"""
        if self.path_planner is None:
            raise ValueError("请先调用 prepare_grid_with_inlet_outlet() 方法")

        print("使用经典A*算法寻找路径...")
        self.performance_analyzer.start_timer('astar_classic')

        path, iterations = self.path_planner.astar_classic(
            self.start_point, self.end_point, allow_diagonal)

        path_time = self.performance_analyzer.end_timer('astar_classic')

        if path:
            self.paths['A* Classic'] = path
            metrics = self.performance_analyzer.calculate_path_metrics(path, self.speed)
            metrics['computation_time'] = path_time
            metrics['iterations'] = iterations
            self.path_metrics['A* Classic'] = metrics
            print(f"A*路径找到，长度: {len(path)}, 耗时: {path_time:.4f}秒")
        else:
            print("A*算法未找到路径")

        return path

    def find_path_dijkstra(self, allow_diagonal: bool = True) -> Optional[List[Tuple[int, int]]]:
        """使用Dijkstra算法寻找路径"""
        if self.path_planner is None:
            raise ValueError("请先调用 prepare_grid_with_inlet_outlet() 方法")

        print("使用Dijkstra算法寻找路径...")
        self.performance_analyzer.start_timer('dijkstra')

        path, iterations = self.path_planner.dijkstra(
            self.start_point, self.end_point, allow_diagonal)

        path_time = self.performance_analyzer.end_timer('dijkstra')

        if path:
            self.paths['Dijkstra'] = path
            metrics = self.performance_analyzer.calculate_path_metrics(path, self.speed)
            metrics['computation_time'] = path_time
            metrics['iterations'] = iterations
            self.path_metrics['Dijkstra'] = metrics
            print(f"Dijkstra路径找到，长度: {len(path)}, 耗时: {path_time:.4f}秒")
        else:
            print("Dijkstra算法未找到路径")

        return path

    def find_path_fluid_astar(self, fluid_weight: float = 0.5,
                            allow_diagonal: bool = True) -> Optional[List[Tuple[int, int]]]:
        """使用流体增强A*算法寻找路径"""
        if self.path_planner is None or self.speed is None:
            raise ValueError("请先运行流体模拟")

        print("使用流体增强A*算法寻找路径...")
        self.performance_analyzer.start_timer('fluid_astar')

        path, iterations = self.path_planner.fluid_enhanced_astar(
            self.start_point, self.end_point, self.speed, self.u, self.v,
            fluid_weight, allow_diagonal)

        path_time = self.performance_analyzer.end_timer('fluid_astar')

        if path:
            self.paths['Fluid A*'] = path
            metrics = self.performance_analyzer.calculate_path_metrics(path, self.speed)
            metrics['computation_time'] = path_time
            metrics['iterations'] = iterations
            metrics['fluid_weight'] = fluid_weight
            self.path_metrics['Fluid A*'] = metrics
            print(f"流体A*路径找到，长度: {len(path)}, 耗时: {path_time:.4f}秒")
        else:
            print("流体A*算法未找到路径")

        return path

    def find_path_velocity_field(self, step_size: float = 0.5,
                               max_steps: int = 10000) -> Optional[List[Tuple[int, int]]]:
        """使用速度场跟随寻找路径"""
        if self.path_planner is None or self.u is None:
            raise ValueError("请先运行流体模拟")

        print("使用速度场跟随寻找路径...")
        self.performance_analyzer.start_timer('velocity_field')

        path = self.path_planner.velocity_field_path(
            self.start_point, self.end_point, self.u, self.v, step_size, max_steps)

        path_time = self.performance_analyzer.end_timer('velocity_field')

        if path:
            self.paths['Velocity Field'] = path
            metrics = self.performance_analyzer.calculate_path_metrics(path, self.speed)
            metrics['computation_time'] = path_time
            metrics['step_size'] = step_size
            self.path_metrics['Velocity Field'] = metrics
            print(f"速度场路径找到，长度: {len(path)}, 耗时: {path_time:.4f}秒")
        else:
            print("速度场算法未找到路径")

        return path

    def compare_all_paths(self) -> Tuple[Dict[str, List[Tuple[int, int]]], Dict[str, Dict[str, float]]]:
        """比较所有路径规划算法"""
        print("\n开始比较所有路径规划算法...")

        # 运行所有算法
        self.find_path_astar_classic()
        self.find_path_dijkstra()
        self.find_path_fluid_astar(fluid_weight=0.7)
        self.find_path_velocity_field()

        print("\n路径比较完成！")
        print("=" * 50)

        # 打印比较结果
        for name, metrics in self.path_metrics.items():
            print(f"\n{name}:")
            for metric, value in metrics.items():
                if isinstance(value, float):
                    print(f"  {metric}: {value:.4f}")
                else:
                    print(f"  {metric}: {value}")

        return self.paths.copy(), self.path_metrics.copy()

    def visualize_fluid_fields(self, save_path: Optional[str] = None) -> plt.Figure:
        """可视化流体场"""
        if self.speed is None:
            raise ValueError("请先运行流体模拟")

        return self.visualizer.visualize_fluid_fields(
            self.grid_map, self.u, self.v, self.rho, self.speed,
            self.vorticity, self.convergence_history, save_path)

    def visualize_paths(self, save_path: Optional[str] = None) -> plt.Figure:
        """可视化路径比较"""
        if not self.paths:
            raise ValueError("请先运行路径规划算法")

        return self.visualizer.visualize_path_comparison(
            self.grid_map, self.speed, self.paths, self.path_metrics, save_path)

    def visualize_3d_fields(self, field_name: str = 'speed') -> plt.Figure:
        """3D可视化流体场"""
        if self.speed is None:
            raise ValueError("请先运行流体模拟")

        field_map = {
            'speed': self.speed,
            'pressure': self.rho,
            'vorticity': self.vorticity,
            'u_velocity': self.u,
            'v_velocity': self.v
        }

        if field_name not in field_map:
            raise ValueError(f"不支持的场类型: {field_name}")

        return self.visualizer.visualize_3d_surface(
            field_map[field_name], f"3D {field_name.title()} Field")

    def save_results(self, base_path: str = "results") -> None:
        """保存所有结果"""
        import os
        os.makedirs(base_path, exist_ok=True)

        # 保存速度场数据
        if self.speed is not None:
            DataProcessor.save_results_to_excel(
                self.speed, f"{base_path}/velocity_field.xlsx", "Speed")
            DataProcessor.save_results_to_excel(
                self.u, f"{base_path}/u_velocity.xlsx", "U_Velocity")
            DataProcessor.save_results_to_excel(
                self.v, f"{base_path}/v_velocity.xlsx", "V_Velocity")
            DataProcessor.save_results_to_excel(
                self.rho, f"{base_path}/pressure_field.xlsx", "Pressure")

        # 保存路径数据
        for name, path in self.paths.items():
            if path:
                path_array = np.array(path)
                DataProcessor.save_results_to_excel(
                    path_array, f"{base_path}/path_{name.replace(' ', '_').lower()}.xlsx", "Path")

        # 保存性能指标
        if self.path_metrics:
            metrics_data = []
            for alg_name, metrics in self.path_metrics.items():
                row = {'Algorithm': alg_name}
                row.update(metrics)
                metrics_data.append(row)

            import pandas as pd
            df = pd.DataFrame(metrics_data)
            df.to_excel(f"{base_path}/performance_metrics.xlsx", index=False)

        print(f"所有结果已保存到: {base_path}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        summary = {
            'grid_size': self.grid_map.shape if self.grid_map is not None else None,
            'inlet_position': self.start_point,
            'outlet_position': self.end_point,
            'algorithms_tested': list(self.paths.keys()),
            'total_paths_found': len(self.paths),
            'performance_metrics': self.path_metrics.copy(),
            'system_metrics': self.performance_analyzer.get_all_metrics()
        }

        return summary
