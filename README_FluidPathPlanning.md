# 基于流体动力学的路径规划 (Fluid Dynamics-Based Path Planning)

本项目实现了基于流体动力学模拟的路径规划算法。通过模拟流体在障碍物周围的流动，我们可以从流体场中提取信息来指导路径规划算法。

这种方法的优势在于流体自然会寻找绕过障碍物的最优路径，从而为路径规划提供了一种新的视角。

## 概述 (Overview)

该实现由两个主要组件组成：

1. **格子玻尔兹曼方法(LBM)模拟**：模拟流体流动，其中：
   - 起点被指定为入口（流体流入点）
   - 终点被指定为出口（流体流出点）
   - 障碍物被表示为固体边界（流体不能穿过）

2. **路径规划算法**：
   - **方法1**：直接流线跟随（沿着流体流动的路径移动）
   - **方法2**：压力梯度跟随（沿着压力梯度方向移动）
   - **方法3**：改进的A*算法（将流体场信息作为启发式函数的一部分）

## 环境要求 (Requirements)

- Python 3.6+
- NumPy（用于数值计算）
- Matplotlib（用于可视化）
- SciPy（用于科学计算，如高斯滤波）
- (可选) pandas - 用于加载Excel格式的地图

## 文件说明 (Files)

- `FluidPathPlanning.py`：基于流体的路径规划算法的主要实现
- `example_fluid_path_planning.py`：演示如何使用该实现的示例脚本
- `LBM+A20250324.py`：格子玻尔兹曼方法的实现，用于流体模拟
- `AstarClassic.py`：经典A*算法的实现

## 使用方法 (Usage)

### 基本用法 (Basic Usage)

```python
from FluidPathPlanning import FluidPathPlanner
import numpy as np

# 创建一个简单的地图 (0 = 可通行区域, 1 = 障碍物)
grid_map = np.zeros((20, 20), dtype=int)
grid_map[5:15, 10] = 1  # 添加一堵垂直的墙

# 定义起点和终点
start_point = (2, 2)  # 左上角附近
end_point = (17, 17)  # 右下角附近

# 创建规划器
planner = FluidPathPlanner(
    grid_map=grid_map,  # 地图
    start_point=start_point,  # 起点
    end_point=end_point  # 终点
)

# 运行模拟
planner.prepare_grid_with_inlet_outlet()  # 准备网格，设置入口和出口
planner.run_fluid_simulation()  # 运行流体模拟

# 可视化流体场
fluid_fig = planner.visualize_fluid_fields()  # 显示速度场、压力场、流线和涡量场

# 使用不同方法寻找路径
streamline_path = planner.find_path_streamline()  # 流线跟随法
pressure_path = planner.find_path_pressure_gradient()  # 压力梯度跟随法
astar_path = planner.find_path_fluid_astar(fluid_weight=0.7, diagonal_movement=True)  # 流体增强A*算法

# 比较所有路径
paths_fig, paths = planner.compare_paths()  # 在同一图上显示所有路径
```

### 命令行用法 (Command Line Usage)

您也可以直接运行`FluidPathPlanning.py`脚本：

```
python FluidPathPlanning.py --map map_file.xlsx --start 10 10 --end 40 40 --fluid-weight 0.7 --diagonal --save results
```

参数说明:
- `--map`: 地图文件路径（Excel格式）
- `--start`: 起点坐标（行，列）
- `--end`: 终点坐标（行，列）
- `--fluid-weight`: A*算法中流体信息的权重（0-1）
- `--diagonal`: 允许对角线移动
- `--save`: 保存结果到文件

### 示例脚本 (Example Script)

运行示例脚本以查看算法的实际效果：

```
python example_fluid_path_planning.py
```

## 工作原理 (How It Works)

### 1. 流体模拟 (Fluid Simulation)

格子玻尔兹曼方法(LBM)用于模拟流体在障碍物周围的流动。模拟生成几个流体场：

- **速度场 (Velocity field)**：流体流动的方向和大小
- **压力场 (Pressure field)**：流体中的压力分布
- **流线 (Streamlines)**：跟随流体流动的线
- **涡量场 (Vorticity field)**：流体的旋转

### 2. 路径规划方法 (Path Planning Methods)

#### 方法1：流线跟随 (Streamline Following)

该方法沿着流体的流线从起点到终点。由于流体自然会绕过障碍物，这创建了避开障碍物的路径。

流线跟随的优点是路径非常平滑，缺点是可能不是最短路径。

#### 方法2：压力梯度跟随 (Pressure Gradient Following)

该方法沿着压力梯度从高压区到低压区。压力场提供了关于穿过区域的最有效路径的信息。

压力梯度跟随通常会产生比流线跟随更直接的路径。

#### 方法3：流体增强A* (Fluid-Enhanced A*)

该方法通过将流体场信息纳入代价函数来扩展经典的A*算法。代价函数是以下因素的加权组合：

- 传统A*代价（到目标的距离）
- 流体速度（速度越高 = 代价越低）
- 压力梯度（沿着压力梯度 = 代价越低）

通过调整传统A*和流体信息之间的权重，您可以控制路径跟随流体动力学的程度。

## 参数 (Parameters)

### 流体模拟参数 (Fluid Simulation Parameters)

- `max_iter`：模拟的最大迭代次数
- `tau`：松弛时间（控制流体粘性）
- `inlet_velocity`：入口处的速度
- `convergence_threshold`：确定收敛的阈值

### 路径规划参数 (Path Planning Parameters)

- `fluid_weight`：A*算法中流体信息的权重（0-1）
- `diagonal_movement`：是否允许对角线移动

## 自定义 (Customization)

您可以通过以下方式自定义实现：

1. 修改流体模拟参数以改变流动特性
2. 调整A*算法中的流体权重，在最短路径和流体引导路径之间取得平衡
3. 实现使用流体场不同方面的其他路径规划方法

## 参考文献 (References)

- 格子玻尔兹曼方法: https://en.wikipedia.org/wiki/Lattice_Boltzmann_methods
- A*算法: https://en.wikipedia.org/wiki/A*_search_algorithm
- 流体动力学: https://en.wikipedia.org/wiki/Fluid_dynamics
